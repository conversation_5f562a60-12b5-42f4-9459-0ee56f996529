package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款理算配置-费用约束表
 */
@ApiModel("条款理算配置-费用约束表")
@Getter
@Setter
public class ClmsFeeConfig {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 约束等级
     */
    @ApiModelProperty("约束等级")
    private Byte configLevel;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 限额
     */
    @ApiModelProperty("限额")
    private BigDecimal amountLimit;

    /**
     * 差异化限额id-责任明细层级使用
     */
    @ApiModelProperty("差异化限额id-责任明细层级使用")
    private Integer amountLimitDifferId;

    /**
     * 月限额
     */
    @ApiModelProperty("月限额")
    private BigDecimal amountMonthLimit;

    /**
     * 月限额分类
     */
    @ApiModelProperty("月限额分类")
    private String amountMonthLimitType;

    /**
     * 差异化月限额
     */
    @ApiModelProperty("差异化月限额")
    private Integer amountMonthLimitDifferId;

    /**
     * 日限额
     */
    @ApiModelProperty("日限额")
    private BigDecimal amountDayLimit;

    /**
     * 差异化日限额-责任明细层级使用
     */
    @ApiModelProperty("差异化日限额-责任明细层级使用")
    private Integer amountDayLimitDifferId;

    /**
     * 是否存在共享限额
     */
    @ApiModelProperty("是否存在共享限额")
    private Boolean amountShareFlag;

    /**
     * 限制赔付次数
     */
    @ApiModelProperty("限制赔付次数")
    private Integer timesLimit;

    /**
     * 赔付次数定义
     */
    @ApiModelProperty("赔付次数定义")
    private String timesDefine;

    /**
     * 月赔付次数
     */
    @ApiModelProperty("月赔付次数")
    private Integer timesMonthLimit;

    /**
     * 月赔付次数分类
     */
    @ApiModelProperty("月赔付次数分类")
    private String timesMonthLimitType;

    /**
     * 月赔付次数定义
     */
    @ApiModelProperty("月赔付次数定义")
    private String timesMonthDefine;

    /**
     * 日赔付次数
     */
    @ApiModelProperty("日赔付次数")
    private Integer timesDayLimit;

    /**
     * 日赔付次数定义
     */
    @ApiModelProperty("日赔付次数定义")
    private String timesDayDefine;

    /**
     * 是否存在共享限次
     */
    @ApiModelProperty("是否存在共享限次")
    private Boolean timesShareFlag;

    /**
     * 年免赔额
     */
    @ApiModelProperty("年免赔额")
    private BigDecimal yearDeductible;

    /**
     * 日免赔额
     */
    @ApiModelProperty("日免赔额")
    private BigDecimal dayDeductible;

    /**
     * 次免赔额
     */
    @ApiModelProperty("次免赔额")
    private BigDecimal timesDeductible;

    /**
     * 次免赔额次数定义
     */
    @ApiModelProperty("次免赔额次数定义")
    private String timesDeductibleDefine;

    /**
     * 是否存在共享免赔
     */
    @ApiModelProperty("是否存在共享免赔")
    private Boolean deductibleShareFlag;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
