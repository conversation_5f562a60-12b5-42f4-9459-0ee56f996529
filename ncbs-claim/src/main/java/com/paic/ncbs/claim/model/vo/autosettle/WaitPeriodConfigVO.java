package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 等待期配置VO
 */
@ApiModel("等待期配置VO")
@Getter
@Setter
public class WaitPeriodConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 责任明细分类
     */
    @ApiModelProperty("责任明细分类")
    private String dutyDetailType;

    /**
     * 新保疾病等待期（天）
     */
    @ApiModelProperty("新保疾病等待期（天）")
    private Integer diseaseWaitPeriod;

    /**
     * 新保意外等待期（天）
     */
    @ApiModelProperty("新保意外等待期（天）")
    private Integer accidentWaitPeriod;
}
