package com.paic.ncbs.claim.controller.autosettle;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.autosettle.*;
import com.paic.ncbs.claim.service.autosettle.ScenarioConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 场景配置控制器
 */
@RestController
@RequestMapping("/api/autosettle/scenario")
@Api(tags = "场景配置管理")
public class ScenarioConfigController extends BaseController {

    @Autowired
    private ScenarioConfigService scenarioConfigService;

    /**
     * 获取场景基础信息
     */
    @GetMapping("/config")
    @ApiOperation("获取场景基础信息")
    public ScenarioConfigVO getScenarioConfig(
            @ApiParam("产品代码") @RequestParam String productCode,
            @ApiParam("险种代码") @RequestParam String planCode,
            @ApiParam("方案代码") @RequestParam String riskGroupCode) {
        try {
            if (StringUtils.isBlank(productCode) || StringUtils.isBlank(planCode) || StringUtils.isBlank(riskGroupCode)) {
                throw new GlobalBusinessException("产品代码、险种代码和方案代码不能为空");
            }
            
            return scenarioConfigService.getScenarioConfig(productCode, planCode, riskGroupCode);
        } catch (Exception e) {
            LogUtil.audit("获取场景基础信息失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取场景基础信息失败：" + e.getMessage());
        }
    }

    /**
     * 保存场景基础信息
     */
    @PostMapping("/config")
    @ApiOperation("保存场景基础信息")
    public String saveScenarioConfig(@RequestBody ScenarioConfigVO scenarioConfigVO) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }
            
            scenarioConfigService.saveScenarioConfig(scenarioConfigVO, userUM);
            return "保存成功";
        } catch (Exception e) {
            LogUtil.audit("保存场景基础信息失败：{}", e.getMessage());
            throw new GlobalBusinessException("保存场景基础信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取可选择的责任明细列表
     */
    @GetMapping("/duty-details")
    @ApiOperation("获取可选择的责任明细列表")
    public List<DutyDetailSelectionVO> getSelectableDutyDetails(
            @ApiParam("产品代码") @RequestParam String productCode,
            @ApiParam("险种代码") @RequestParam String planCode,
            @ApiParam("方案代码") @RequestParam String riskGroupCode) {
        try {
            if (StringUtils.isBlank(productCode) || StringUtils.isBlank(planCode) || StringUtils.isBlank(riskGroupCode)) {
                throw new GlobalBusinessException("产品代码、险种代码和方案代码不能为空");
            }
            
            return scenarioConfigService.getSelectableDutyDetails(productCode, planCode, riskGroupCode);
        } catch (Exception e) {
            LogUtil.audit("获取可选择的责任明细列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取可选择的责任明细列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取医院范围配置列表
     */
    @GetMapping("/hospital-range")
    @ApiOperation("获取医院范围配置列表")
    public List<HospitalRangeConfigVO> getHospitalRangeConfigs(
            @ApiParam("配置ID") @RequestParam Integer configId) {
        try {
            if (configId == null) {
                throw new GlobalBusinessException("配置ID不能为空");
            }
            
            return scenarioConfigService.getHospitalRangeConfigs(configId);
        } catch (Exception e) {
            LogUtil.audit("获取医院范围配置列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取医院范围配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 保存医院范围配置
     */
    @PostMapping("/hospital-range")
    @ApiOperation("保存医院范围配置")
    public String saveHospitalRangeConfig(@RequestBody HospitalRangeConfigVO hospitalRangeConfigVO) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }

            scenarioConfigService.saveHospitalRangeConfig(hospitalRangeConfigVO, userUM);
            return "保存成功";
        } catch (Exception e) {
            LogUtil.audit("保存医院范围配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("保存医院范围配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除医院范围配置
     */
    @DeleteMapping("/hospital-range/{id}")
    @ApiOperation("删除医院范围配置")
    public String deleteHospitalRangeConfig(@ApiParam("配置ID") @PathVariable Integer id) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }

            scenarioConfigService.deleteHospitalRangeConfig(id, userUM);
            return "删除成功";
        } catch (Exception e) {
            LogUtil.audit("删除医院范围配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("删除医院范围配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取疾病范围配置列表
     */
    @GetMapping("/disease-range")
    @ApiOperation("获取疾病范围配置列表")
    public List<DiseaseRangeConfigVO> getDiseaseRangeConfigs(
            @ApiParam("配置ID") @RequestParam Integer configId) {
        try {
            if (configId == null) {
                throw new GlobalBusinessException("配置ID不能为空");
            }
            
            return scenarioConfigService.getDiseaseRangeConfigs(configId);
        } catch (Exception e) {
            LogUtil.audit("获取疾病范围配置列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取疾病范围配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 保存疾病范围配置
     */
    @PostMapping("/disease-range")
    @ApiOperation("保存疾病范围配置")
    public String saveDiseaseRangeConfig(@RequestBody DiseaseRangeConfigVO diseaseRangeConfigVO) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }
            
            scenarioConfigService.saveDiseaseRangeConfig(diseaseRangeConfigVO, userUM);
            return "保存成功";
        } catch (Exception e) {
            LogUtil.audit("保存疾病范围配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("保存疾病范围配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除疾病范围配置
     */
    @DeleteMapping("/disease-range/{id}")
    @ApiOperation("删除疾病范围配置")
    public String deleteDiseaseRangeConfig(@ApiParam("配置ID") @PathVariable Integer id) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }
            
            scenarioConfigService.deleteDiseaseRangeConfig(id, userUM);
            return "删除成功";
        } catch (Exception e) {
            LogUtil.audit("删除疾病范围配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("删除疾病范围配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取三目录除外配置列表
     */
    @GetMapping("/catalog3-exclude")
    @ApiOperation("获取三目录除外配置列表")
    public List<Catalog3ExcludeConfigVO> getCatalog3ExcludeConfigs(
            @ApiParam("配置ID") @RequestParam Integer configId) {
        try {
            if (configId == null) {
                throw new GlobalBusinessException("配置ID不能为空");
            }
            
            return scenarioConfigService.getCatalog3ExcludeConfigs(configId);
        } catch (Exception e) {
            LogUtil.audit("获取三目录除外配置列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取三目录除外配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 保存三目录除外配置
     */
    @PostMapping("/catalog3-exclude")
    @ApiOperation("保存三目录除外配置")
    public String saveCatalog3ExcludeConfig(@RequestBody Catalog3ExcludeConfigVO catalog3ExcludeConfigVO) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }
            
            scenarioConfigService.saveCatalog3ExcludeConfig(catalog3ExcludeConfigVO, userUM);
            return "保存成功";
        } catch (Exception e) {
            LogUtil.audit("保存三目录除外配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("保存三目录除外配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除三目录除外配置
     */
    @DeleteMapping("/catalog3-exclude/{id}")
    @ApiOperation("删除三目录除外配置")
    public String deleteCatalog3ExcludeConfig(@ApiParam("配置ID") @PathVariable Integer id) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }
            
            scenarioConfigService.deleteCatalog3ExcludeConfig(id, userUM);
            return "删除成功";
        } catch (Exception e) {
            LogUtil.audit("删除三目录除外配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("删除三目录除外配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取其他核责配置列表
     */
    @GetMapping("/other-verify")
    @ApiOperation("获取其他核责配置列表")
    public List<OtherVerifyConfigVO> getOtherVerifyConfigs(
            @ApiParam("配置ID") @RequestParam Integer configId) {
        try {
            if (configId == null) {
                throw new GlobalBusinessException("配置ID不能为空");
            }
            
            return scenarioConfigService.getOtherVerifyConfigs(configId);
        } catch (Exception e) {
            LogUtil.audit("获取其他核责配置列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取其他核责配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 保存其他核责配置
     */
    @PostMapping("/other-verify")
    @ApiOperation("保存其他核责配置")
    public String saveOtherVerifyConfig(@RequestBody OtherVerifyConfigVO otherVerifyConfigVO) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }
            
            scenarioConfigService.saveOtherVerifyConfig(otherVerifyConfigVO, userUM);
            return "保存成功";
        } catch (Exception e) {
            LogUtil.audit("保存其他核责配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("保存其他核责配置失败：" + e.getMessage());
        }
    }

    /**
     * 删除其他核责配置
     */
    @DeleteMapping("/other-verify/{id}")
    @ApiOperation("删除其他核责配置")
    public String deleteOtherVerifyConfig(@ApiParam("配置ID") @PathVariable Integer id) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }

            scenarioConfigService.deleteOtherVerifyConfig(id, userUM);
            return "删除成功";
        } catch (Exception e) {
            LogUtil.audit("删除其他核责配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("删除其他核责配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取等待期配置列表
     */
    @GetMapping("/wait-period")
    @ApiOperation("获取等待期配置列表")
    public List<WaitPeriodConfigVO> getWaitPeriodConfigs(
            @ApiParam("配置ID") @RequestParam Integer configId) {
        try {
            if (configId == null) {
                throw new GlobalBusinessException("配置ID不能为空");
            }

            return scenarioConfigService.getWaitPeriodConfigs(configId);
        } catch (Exception e) {
            LogUtil.audit("获取等待期配置列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取等待期配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 保存等待期配置
     */
    @PostMapping("/wait-period")
    @ApiOperation("保存等待期配置")
    public String saveWaitPeriodConfig(@RequestBody WaitPeriodConfigVO waitPeriodConfigVO) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }

            scenarioConfigService.saveWaitPeriodConfig(waitPeriodConfigVO, userUM);
            return "保存成功";
        } catch (Exception e) {
            LogUtil.audit("保存等待期配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("保存等待期配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取责任理算顺序配置列表
     */
    @GetMapping("/duty-calc-order")
    @ApiOperation("获取责任理算顺序配置列表")
    public List<DutyCalcOrderConfigVO> getDutyCalcOrderConfigs(
            @ApiParam("配置ID") @RequestParam Integer configId) {
        try {
            if (configId == null) {
                throw new GlobalBusinessException("配置ID不能为空");
            }

            return scenarioConfigService.getDutyCalcOrderConfigs(configId);
        } catch (Exception e) {
            LogUtil.audit("获取责任理算顺序配置列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取责任理算顺序配置列表失败：" + e.getMessage());
        }
    }

    /**
     * 保存责任理算顺序配置
     */
    @PostMapping("/duty-calc-order")
    @ApiOperation("保存责任理算顺序配置")
    public String saveDutyCalcOrderConfig(@RequestBody List<DutyCalcOrderConfigVO> dutyCalcOrderConfigVOs) {
        try {
            String userUM = getCurrentUser().getUserCode();
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }

            scenarioConfigService.saveDutyCalcOrderConfig(dutyCalcOrderConfigVOs, userUM);
            return "保存成功";
        } catch (Exception e) {
            LogUtil.audit("保存责任理算顺序配置失败：{}", e.getMessage());
            throw new GlobalBusinessException("保存责任理算顺序配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用的核责规则列表
     */
    @GetMapping("/verify-rules")
    @ApiOperation("获取可用的核责规则列表")
    public List<VerifyRuleVO> getAvailableVerifyRules() {
        try {
            return scenarioConfigService.getAvailableVerifyRules();
        } catch (Exception e) {
            LogUtil.audit("获取可用的核责规则列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取可用的核责规则列表失败：" + e.getMessage());
        }
    }

    /**
     * 搜索医院
     */
    @GetMapping("/search/hospitals")
    @ApiOperation("搜索医院")
    public List<HospitalVO> searchHospitals(
            @ApiParam("医院名称") @RequestParam String hospitalName,
            @ApiParam("机构类型") @RequestParam(required = false) String orgType) {
        try {
            if (StringUtils.isBlank(hospitalName)) {
                throw new GlobalBusinessException("医院名称不能为空");
            }

            return scenarioConfigService.searchHospitals(hospitalName, orgType);
        } catch (Exception e) {
            LogUtil.audit("搜索医院失败：{}", e.getMessage());
            throw new GlobalBusinessException("搜索医院失败：" + e.getMessage());
        }
    }

    /**
     * 搜索疾病ICD
     */
    @GetMapping("/search/disease-icds")
    @ApiOperation("搜索疾病ICD")
    public List<DiseaseIcdVO> searchDiseaseIcds(
            @ApiParam("搜索关键字") @RequestParam String searchStr,
            @ApiParam("诊断分类代码") @RequestParam(required = false) String diagnosisCategoryCode) {
        try {
            if (StringUtils.isBlank(searchStr)) {
                throw new GlobalBusinessException("搜索关键字不能为空");
            }

            return scenarioConfigService.searchDiseaseIcds(searchStr, diagnosisCategoryCode);
        } catch (Exception e) {
            LogUtil.audit("搜索疾病ICD失败：{}", e.getMessage());
            throw new GlobalBusinessException("搜索疾病ICD失败：" + e.getMessage());
        }
    }

    /**
     * 搜索药品
     */
    @GetMapping("/search/medicines")
    @ApiOperation("搜索药品")
    public List<MedicineVO> searchMedicines(
            @ApiParam("药品名称") @RequestParam(required = false) String medicineName,
            @ApiParam("药品包ID") @RequestParam(required = false) Integer packageId) {
        try {
            if (StringUtils.isBlank(medicineName) && packageId == null) {
                throw new GlobalBusinessException("药品名称或药品包ID不能同时为空");
            }

            return scenarioConfigService.searchMedicines(medicineName, packageId);
        } catch (Exception e) {
            LogUtil.audit("搜索药品失败：{}", e.getMessage());
            throw new GlobalBusinessException("搜索药品失败：" + e.getMessage());
        }
    }

    /**
     * 获取省市区列表
     */
    @GetMapping("/regions")
    @ApiOperation("获取省市区列表")
    public List<ExcludeRegionVO> getRegionList() {
        try {
            return scenarioConfigService.getRegionList();
        } catch (Exception e) {
            LogUtil.audit("获取省市区列表失败：{}", e.getMessage());
            throw new GlobalBusinessException("获取省市区列表失败：" + e.getMessage());
        }
    }
}
