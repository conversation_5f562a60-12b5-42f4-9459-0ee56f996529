package com.paic.ncbs.claim.service.settle.impl;

import com.github.pagehelper.PageHelper;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.CaseProcessStatus;
import com.paic.ncbs.claim.common.page.PageResult;
import com.paic.ncbs.claim.common.util.*;
import com.paic.ncbs.claim.controller.standard.bill.enums.BillEnum;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoExEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.antimoneylaundering.AntiMoneyLaunderingMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.*;
import com.paic.ncbs.claim.dao.mapper.duty.OperationDefineMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseClassMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.other.SurveyMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoExMapper;
import com.paic.ncbs.claim.dao.mapper.settle.*;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TravelAlertMapper;
import com.paic.ncbs.claim.dao.mapper.verify.VerifyMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.antimoneylaundering.ClmsAntiMoneyLaunderingInfoDto;
import com.paic.ncbs.claim.model.dto.checkloss.ExaminFailDTO;
import com.paic.ncbs.claim.model.dto.checkloss.HospitalInfoDTO;
import com.paic.ncbs.claim.model.dto.checkloss.LossReduceDTO;
import com.paic.ncbs.claim.model.dto.checkloss.PropertyLossDTO;
import com.paic.ncbs.claim.model.dto.duty.*;
import com.paic.ncbs.claim.model.dto.endcase.CaseInfoParameterDTO;
import com.paic.ncbs.claim.model.dto.other.CityDefineDTO;
import com.paic.ncbs.claim.model.dto.pay.PaymentInfoDTO;
import com.paic.ncbs.claim.model.dto.settle.*;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TravelAlertDTO;
import com.paic.ncbs.claim.model.dto.verify.VerifyDTO;
import com.paic.ncbs.claim.model.vo.duty.*;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.model.vo.settle.*;
import com.paic.ncbs.claim.service.ahcs.AhcsCommonService;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.checkloss.ChannelProcessService;
import com.paic.ncbs.claim.service.checkloss.HospitalInfoService;
import com.paic.ncbs.claim.service.common.IHospitalInfoPlusService;
import com.paic.ncbs.claim.service.customer.CustomerInfoService;
import com.paic.ncbs.claim.service.duty.DutySurveyService;
import com.paic.ncbs.claim.service.endcase.CaseProcessService;
import com.paic.ncbs.claim.service.endcase.DutyBillLimitInfoService;
import com.paic.ncbs.claim.service.match.InvoiceMatchService;
import com.paic.ncbs.claim.service.other.CityDefineService;
import com.paic.ncbs.claim.service.pay.PaymentInfoService;
import com.paic.ncbs.claim.service.settle.MedicalBillService;
import com.paic.ncbs.claim.service.settle.PolicyPayService;
import com.paic.ncbs.claim.service.taskdeal.TaskInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.paic.ncbs.claim.common.util.BigDecimalUtils.nvl;


@Service("medicalBillService")
public class MedicalBillServiceImpl implements MedicalBillService {

    @Autowired
    private MedicalBillInfoMapper medicalBillInfoMapper;
    @Autowired
    private MedicalBillSpecialMapper medicalBillSpecialMapper;
    @Autowired
    private MedicalBillReduceDetailMapper medicalBillReduceDetailMapper;
    @Resource(name = "channelProcessService")
    private ChannelProcessService channelProcessService;
    @Autowired
    private FlightDelayMapper flightDelayMapper;
    @Autowired
    private ExaminFailMapper examinFailMapper;
    @Autowired
    private TravelAlertMapper travelAlertMapper;
    @Autowired
    private PropertyLossMapper propertyLossMapper;
    @Autowired
    private OtherLossMapper otherLossMapper;
    @Autowired
    private CaseClassMapper caseClassDao;
    @Autowired
    private MedicalBillDetailMapper medicalBillDetailMapper;

    @Autowired
    private CaseProcessService caseProcessService;
    @Autowired
    private AhcsCommonService ahcsCommonService;

    @Autowired
    private HospitalInfoService hospitalInfoService;

    @Autowired
    private OperationDefineMapper operationDefineMapper;

    @Lazy
    @Autowired
    private DutySurveyService dutySurveyService;

    @Autowired
    TaskInfoService taskInfoService;

    @Autowired
    DutyBillLimitInfoMapper dutyBillLimitInfoMapper;

    @Autowired
    PolicyInfoMapper policyInfoMapper;
    @Autowired
    private CityDefineService cityDefineService;

    @Autowired
    private DiagnoseHospitalBillAssociationMapper diagnoseHospitalBillAssociationMapper;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private CommonParameterMapper commonParameterMapper;
    @Autowired
    private PaymentInfoService paymentInfoService;
    @Autowired
    AntiMoneyLaunderingMapper antiMoneyLaunderingMapper;
    @Autowired
    private ReportInfoExMapper reportInfoExMapper;
    @Autowired
    private OcasMapper ocasMapper;

    private static final String REMOVE_BILL_INFO_LIST = "removeBillInfoList";
    private static final String REMOVE_BILL_DETAIL_LIST = "removeBillDetailList";
    private static final String REMOVE_BILL_REDUCE_DETAIL_LIST = "removeBillReduceDetailList";
    private static final String ADD_BILL_DETAIL_LIST = "addBillDetailList";

    @Autowired
    private PersonDiagnoseMapper personDiagnoseDao;

    @Autowired
    private PersonDiseaseMapper personDiseaseDao;

    @Autowired
    private PersonAccidentMapper personAccidentDao;
    @Autowired
    private HospitalInfoMapper hospitalInfoMapper;
    @Autowired
    private SurveyMapper surveyMapper;
    @Autowired
    private WesureSettleMapper wesureSettleMapper;
    @Value("${wesure.medical.bill.offset:true}")
    private boolean wesureMedicalBillOffSet;
    @Autowired
    private PolicyPayService policyPayService;
    @Autowired
    private LossReduceMapper lossReduceMapper;
    @Autowired
    private VerifyMapper verifyMapper;
    @Autowired
    private PolicyBatchMapper policyBatchMapper;
    @Autowired
    private DutyBillLimitInfoService dutyBillLimitInfoService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private PaymentInfoMapper paymentInfoMapper;

    @Autowired
    private CustomerInfoService customerInfoService;

    @Autowired
    private IHospitalInfoPlusService hospitalInfoPlusService;

    @Autowired
    private InvoiceMatchService invoiceMatchService;

    @Transactional
    @Override
    public CompareBillNoResultVO validAndAddMedicalBill(MedicalBillInfoVO medicalBillInfoVO, String userUM) {

        //校验相关金额关系
        checkAmount(medicalBillInfoVO);
        //校验是否已有发票号(MT01单张 MT02境外)
//        if (ChecklossConst.BILL_TYPE_ONE.equals(medicalBillInfoVO.getBillClass())) {
//            CompareBillNoResultVO compareBillNoResultVO = medicalBillInfoMapper.validBillExist(medicalBillInfoVO);
//            if (compareBillNoResultVO != null) {
//                LogUtil.audit("#发票号在本案下已有保存！");
//                return compareBillNoResultVO;
//            }
//        }
        //比较‘年度医疗保险基金累计支付金额’insuranceTotalPayment, ‘第三方支付金额’prepaidAmount和；若第三方支付金额大则抛异常
        //this.checkSpecialBill(medicalBillInfoVO);

        // 人工匹责验证：当产品大类=健康险/意外险，且个险时，录入定损信息保存时，需要关联至少一个责任明细
//        validateManualMatchForHealthAndAccident(medicalBillInfoVO);

        this.addMedicalBill(medicalBillInfoVO, userUM);
        return null;
    }

    @Transactional
    @Override
    public void clearBillAmonutForHuge(String reportNo, Integer caseTimes, String userId, String lossObjectNo) throws GlobalBusinessException {
        List<String> idBillInfoList = null;

        idBillInfoList = medicalBillInfoMapper.getIdBillInfoList(reportNo, caseTimes);

        if (ListUtils.isNotEmpty(idBillInfoList) && StringUtils.isNotBlank(idBillInfoList.get(0))) {
            medicalBillInfoMapper.clearBillInfoAmonut(userId, reportNo, caseTimes, lossObjectNo);
            ahcsCommonService.batchHandlerTransactional(MedicalBillInfoMapper.class, idBillInfoList, 40, "clearBillDetailAmonut", userId);
            ahcsCommonService.batchHandlerTransactionalWithArgs(MedicalBillInfoMapper.class, idBillInfoList, ListUtils.GROUP_NUM, REMOVE_BILL_REDUCE_DETAIL_LIST, userId);
            medicalBillSpecialMapper.removeBillSpecialList(idBillInfoList, userId);
        }
    }

    /**
     * 人工匹责验证：当产品大类=健康险/意外险，且个险时，录入定损信息保存时，需要关联至少一个责任明细
     *
     * @param medicalBillInfoVO
     * @throws GlobalBusinessException
     */
    private void validateManualMatchForHealthAndAccident(MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException {
        try {
            Map<String, String> productInfo = ocasMapper.getProductInfoByReportNoAndCaseTimes(medicalBillInfoVO.getReportNo(), String.valueOf(medicalBillInfoVO.getCaseTimes()));
            if (productInfo == null || productInfo.isEmpty()) {
                return;
            }
            String productCode = productInfo.get("productCode");

            if (StringUtils.isNotBlank(productCode)) {
                invoiceMatchService.validateManualMatch(
                    medicalBillInfoVO.getReportNo(),
                    medicalBillInfoVO.getCaseTimes(),
                    productCode
                );
            }
        } catch (Exception e) {
            LogUtil.audit("人工匹责验证失败：{}", e.getMessage());
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    /**
     * 账单录入金额校验
     *
     * @param medicalBillInfoVO
     * @throws GlobalBusinessException
     */
    private void checkAmount(MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException {
        //账单金额
        BigDecimal billAmount = medicalBillInfoVO.getBillAmount();
        //自费金额
        BigDecimal deductibleAmount = medicalBillInfoVO.getDeductibleAmount();
        //部分自费金额
        BigDecimal partialDeductible = medicalBillInfoVO.getPartialDeductible();
        //第三方支付金额
        BigDecimal prepaidAmount = medicalBillInfoVO.getPrepaidAmount();
        //不合理金额
        BigDecimal immoderateAmount = medicalBillInfoVO.getImmoderateAmount();

        boolean billAmountIsNull = billAmount == null;
        boolean deductibleAmountIsNull = deductibleAmount == null;
        boolean partialDeductibleIsNull = partialDeductible == null;
        boolean prepaidAmountIsNull = prepaidAmount == null;
        boolean immoderateAmountIsNull = immoderateAmount == null;

        //账单金额不为空
        if (!billAmountIsNull) {
            if (billAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "账单金额必须大于等于0");
            }

            if (!deductibleAmountIsNull) {
                if (deductibleAmount.compareTo(billAmount) > 0) {
                    throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "自费金额必须小于等于账单金额");
                }
            }

            if (!partialDeductibleIsNull) {
                if (partialDeductible.compareTo(billAmount) > 0) {
                    throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "部分自费金额必须小于等于账单金额");
                }
            }

            if (!prepaidAmountIsNull) {
                if (prepaidAmount.compareTo(billAmount) > 0) {
                    throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "第三方支付金额必须小于等于账单金额");
                }
            }

            if (!immoderateAmountIsNull) {
                if (immoderateAmount.compareTo(billAmount) > 0) {
                    throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "不合理金额必须小于等于账单金额");
                }
            }
        }

        if (!deductibleAmountIsNull) {
            if (deductibleAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "自费金额必须大于等于0");
            }
        }

        if (!partialDeductibleIsNull) {
            if (partialDeductible.compareTo(BigDecimal.ZERO) < 0) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "部分自费金额必须大于等于0");
            }
        }

        if (!prepaidAmountIsNull) {
            if (prepaidAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "第三方支付金额必须大于等于0");
            }
        }

        if (!immoderateAmountIsNull) {
            if (immoderateAmount.compareTo(BigDecimal.ZERO) < 0) {
                throw new GlobalBusinessException(ErrorCode.Core.THROW_EXCEPTION_MSG, "不合理金额必须大于等于0");
            }
        }
    }


    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public MedicalBillInfoPageVO getBillInfoByPage(MedicalBillInfoDTO medicalBillInfoDTO) throws GlobalBusinessException {
//        medicalBillInfoDTO.setCaseTimes(null);
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        String reportNo = medicalBillInfoDTO.getReportNo();
        Integer caseTimes = medicalBillInfoDTO.getCaseTimes();
//        String lossObjectNo = medicalBillInfoDTO.getLossObjectNo();
        int currentPage = medicalBillInfoDTO.getCurrentPage();
        int perPageSize = medicalBillInfoDTO.getPerPageSize();

        medicalBillInfoDTO.setOrderColumn(ChecklossConst.BILL_ORDER_COLUMN_MAP.get(medicalBillInfoDTO.getOrderColumn()));
        medicalBillInfoDTO.setOrderType(ChecklossConst.BILL_ORDER_TYPE_MAP.get(medicalBillInfoDTO.getOrderType()));

        //计算发票总额
        MedicalBillInfoPageVO medicalBillInfoPage = Optional.ofNullable(medicalBillInfoMapper.getMedicalBillAllSumAmount(medicalBillInfoDTO))
                .orElse(new MedicalBillInfoPageVO());
        //查询发票列表
        PageHelper.startPage(currentPage, perPageSize);
        List<MedicalBillInfoDTO> medicalBillInfoList = medicalBillInfoMapper.getBillInfoByPage(medicalBillInfoDTO);
        PageResult<MedicalBillInfoDTO> pageResult = new PageResult<>(medicalBillInfoList);
        //查询发票id
        List<String> idBillInfoList = medicalBillInfoMapper.getIdBillInfoList(reportNo, caseTimes);

        //待梳理
//        List<CompareBillNoResultVO> compareBillNoResultVOList = medicalBillInfoMapper.getOtherCaseSameBillNo(reportNo, lossObjectNo);
//        if (ListUtils.isNotEmpty(compareBillNoResultVOList)) {
//            Set<String> sameBillNoSet = new HashSet<>();
//            for (CompareBillNoResultVO compareBillNoResultVO : compareBillNoResultVOList) {
//                sameBillNoSet.add(compareBillNoResultVO.getBillNo());
//            }
//            Iterator iterator = sameBillNoSet.iterator();
//            String billNo = "";
//            List<CompareBillNoResultVO> otherCaseSameBillList = new ArrayList<>();
//            CompareBillNoResultVO compareBillNoResult = null;
//            while (iterator.hasNext()) {
//                billNo = (String) iterator.next();
//                compareBillNoResult = new CompareBillNoResultVO();
//                compareBillNoResult.setBillNo(billNo);
//                List<CompareBillNoResultVO> otherCaseSameBillListVO = new ArrayList<>();
//                for (CompareBillNoResultVO compareBillNoResultVO : compareBillNoResultVOList) {
//                    if (billNo.equals(compareBillNoResultVO.getBillNo())) {
//                        otherCaseSameBillListVO.add(compareBillNoResultVO);
//                    }
//                }
//                compareBillNoResult.setOtherCaseSameBillList(otherCaseSameBillListVO);
//                otherCaseSameBillList.add(compareBillNoResult);
//            }
//            medicalBillInfoPage.setOtherCaseSameBillList(otherCaseSameBillList);
//        }

        medicalBillInfoPage.setIdAhcsBillInfoList(idBillInfoList);
        medicalBillInfoPage.setPageResult(pageResult);
        return medicalBillInfoPage;
    }

    @Transactional
    @Override
    public void removeMedicalBill(IdAhcsBillInfoListVO idAhcsBillInfoListVO, String userUM) throws Exception {
        List<String> idAhcsBillInfoList = idAhcsBillInfoListVO.getIdAhcsBillInfoList();
        if(CollectionUtils.isNotEmpty(idAhcsBillInfoList)) {
        medicalBillSpecialMapper.removeBillSpecialList(idAhcsBillInfoList, userUM);
        ahcsCommonService.batchHandlerTransactional(MedicalBillInfoMapper.class, idAhcsBillInfoList, 40, REMOVE_BILL_INFO_LIST, userUM);
        ahcsCommonService.batchHandlerTransactional(MedicalBillDetailMapper.class, idAhcsBillInfoList, 40, REMOVE_BILL_DETAIL_LIST, userUM);
        ahcsCommonService.batchHandlerTransactionalWithArgs(MedicalBillReduceDetailMapper.class, idAhcsBillInfoList, ListUtils.GROUP_NUM, REMOVE_BILL_REDUCE_DETAIL_LIST, userUM);
        }
    }


    public List<MedicalBillReduceDetailDTO> getBillReduceDetailList(String idAhcsBillInfo) {
        return medicalBillReduceDetailMapper.getBillReduceDetailList(idAhcsBillInfo);
    }


    private void addMedicalBill(MedicalBillInfoVO medicalBillInfoVO, String userUM) {
        String idAhcsBillInfo = UuidUtil.getUUID();
        medicalBillInfoVO.setIdAhcsBillInfo(idAhcsBillInfo);
        //根据报案号和赔付次数 得出 归档时间
        Date archiveTime = caseProcessService.getArchiveTimeByReportNo(medicalBillInfoVO.getReportNo(), medicalBillInfoVO.getCaseTimes());
        LogUtil.audit("#是否需要保存费用扣费明细，reportNo=%s,saveReduceDetail=%s", medicalBillInfoVO.getReportNo(), medicalBillInfoVO.getSaveReduceDetail());
        //批量新增账单费用扣费明细表信息 CLMS_BILL_REDUCE_DETAIL
        if (StringUtils.isEmpty(medicalBillInfoVO.getSaveReduceDetail()) || ChecklossConst.TRUE.equals(medicalBillInfoVO.getSaveReduceDetail())) {
            List<MedicalBillReduceDetailDTO> billReduceDetailList = medicalBillInfoVO.getMedicalBillReduceDetailDTOList();
            this.addBillReduceDetailList(billReduceDetailList, userUM, idAhcsBillInfo, archiveTime);
        }

        //新增账单详情 clms_bill_detail
        List<MedicalBillDetailDTO> medicalBillDetailDTOList = this.getBillDetailList(medicalBillInfoVO, userUM, archiveTime);
        if (CollectionUtils.isNotEmpty(medicalBillDetailDTOList)) {
            medicalBillDetailMapper.addBillDetailList(medicalBillDetailDTOList);
        }

        //新增账单信息
        medicalBillInfoVO.setIdClmChannelProcess(this.getChannelProcessId(medicalBillInfoVO, userUM));
        MedicalBillInfoVO medicalBillInfo = this.getMedicalInfo(medicalBillInfoVO, medicalBillDetailDTOList, userUM);
        medicalBillInfo.setArchiveTime(archiveTime);
        medicalBillInfoMapper.addBillInfo(medicalBillInfo);

        //新增监管字段
        //this.needBillSpecial(medicalBillInfoVO);
        this.addBillSpecial(medicalBillInfoVO.getBillSpecial(), userUM, idAhcsBillInfo, archiveTime);

        //将诊断信息存入clms_diagnose_hospital_bill_association表中
        this.saveDiagnoseHospitalBillAssociation(medicalBillInfoVO.getDiagnoseHospitalBillAssociationList(), userUM, idAhcsBillInfo, medicalBillInfoVO.getReportNo());

    }

    private void addBillSpecial(MedicalBillSpecialDTO billSpecial, String userUM, String idAhcsBillInfo, Date archiveTime) {
        if (billSpecial != null) {
            List<String> idAhcsBillInfoList = new ArrayList<>();
            idAhcsBillInfoList.add(idAhcsBillInfo);
            medicalBillSpecialMapper.removeBillSpecialList(idAhcsBillInfoList, userUM);

            billSpecial.setUpdatedBy(userUM);
            billSpecial.setIdAhcsBillInfo(idAhcsBillInfo);
            billSpecial.setCreatedBy(userUM);
            billSpecial.setArchiveTime(archiveTime);
            medicalBillSpecialMapper.addBillSpecial(billSpecial);
        }
    }


    private void addBillReduceDetailList(List<MedicalBillReduceDetailDTO> billReduceDetailList, String userUM, String idAhcsBillInfo, Date archiveTime) {
        if (billReduceDetailList != null && !billReduceDetailList.isEmpty()) {
            for (MedicalBillReduceDetailDTO medicalBillReduceDetail : billReduceDetailList) {
                medicalBillReduceDetail.setIdAhcsBillInfo(idAhcsBillInfo);
                medicalBillReduceDetail.setCreatedBy(userUM);
                medicalBillReduceDetail.setUpdatedBy(userUM);
                medicalBillReduceDetail.setArchiveTime(archiveTime);
            }
            ahcsCommonService.batchHandlerTransactionalWithArgs(MedicalBillReduceDetailMapper.class, billReduceDetailList, ListUtils.GROUP_NUM, "addBillReduceDetailList");
        }
    }


    private List<MedicalBillDetailDTO> getBillDetailList(MedicalBillInfoVO medicalBillInfoVO, String userUM, Date archiveTime) {
        List<MedicalBillDetailDTO> medicalBillDetailDTOList = medicalBillInfoVO.getMedicalBillDetailDTOList();
        for (MedicalBillDetailDTO medicalBillDetail : medicalBillDetailDTOList) {
            if (StringUtils.isNotEmpty(medicalBillInfoVO.getInputMode())) {
                medicalBillDetail.setBillAmount(medicalBillInfoVO.getBillAmount());
                medicalBillDetail.setInputMode(medicalBillInfoVO.getInputMode());
            }
            medicalBillDetail.setCreatedBy(userUM);
            medicalBillDetail.setUpdatedBy(userUM);
            medicalBillDetail.setIdAhcsBillInfo(medicalBillInfoVO.getIdAhcsBillInfo());
            medicalBillDetail.setArchiveTime(archiveTime);
        }
        return medicalBillDetailDTOList;
    }


    private MedicalBillInfoVO getMedicalInfo(MedicalBillInfoVO medicalBillInfoVO,
                                             List<MedicalBillDetailDTO> medicalBillDetailDTOList, String userUM) {
        medicalBillInfoVO.setCreatedBy(userUM);
        medicalBillInfoVO.setUpdatedBy(userUM);

        BigDecimal deductibleAmount = new BigDecimal("0.00");
        BigDecimal partialDeductible = new BigDecimal("0.00");
        BigDecimal immoderateAmount = new BigDecimal("0.00");
        BigDecimal billAmount = new BigDecimal("0.00");
        for (MedicalBillDetailDTO medicalBillDetailDTO : medicalBillDetailDTOList) {
            billAmount = billAmount.add(BigDecimalUtils.convertBigDecimal(medicalBillDetailDTO.getBillAmount()));
            deductibleAmount = deductibleAmount
                    .add(BigDecimalUtils.convertBigDecimal(medicalBillDetailDTO.getDeductibleAmount()));
            partialDeductible = partialDeductible
                    .add(BigDecimalUtils.convertBigDecimal(medicalBillDetailDTO.getPartialDeductible()));
            immoderateAmount = immoderateAmount
                    .add(BigDecimalUtils.convertBigDecimal(medicalBillDetailDTO.getImmoderateAmount()));
        }

        if (StringUtils.isEmpty(medicalBillInfoVO.getInputMode())) {
            medicalBillInfoVO.setBillAmount(billAmount);
        }
        medicalBillInfoVO.setDeductibleAmount(deductibleAmount);
        medicalBillInfoVO.setPartialDeductible(partialDeductible);
        medicalBillInfoVO.setImmoderateAmount(immoderateAmount);

        medicalBillInfoVO.setReasonableAmount(this.getReasonableAmount(medicalBillInfoVO));

        return medicalBillInfoVO;
    }


    private String getChannelProcessId(MedicalBillInfoVO medicalBillInfoVO, String userUM) {
        int caseTimes = medicalBillInfoVO.getCaseTimes();
        String reportNo = medicalBillInfoVO.getReportNo();

        CaseInfoParameterDTO caseInfoParameter = new CaseInfoParameterDTO();
        caseInfoParameter.setReportNo(reportNo);
        caseInfoParameter.setCaseTimes(caseTimes);
        caseInfoParameter.setChannelType(ChecklossConst.CASECLASS_PEOPLE_HURT);
        caseInfoParameter.setUserId(userUM);
        return channelProcessService.getChannelProcessId(caseInfoParameter);
    }


    private BigDecimal getReasonableAmount(MedicalBillInfoVO medicalBillInfoVO) {
        BigDecimal billAmount = medicalBillInfoVO.getBillAmount();

        BigDecimal deductibleAmount = medicalBillInfoVO.getDeductibleAmount();
        deductibleAmount = BigDecimalUtils.convertBigDecimal(deductibleAmount);

        BigDecimal partialDeductible = medicalBillInfoVO.getPartialDeductible();
        partialDeductible = BigDecimalUtils.convertBigDecimal(partialDeductible);

        BigDecimal immoderateAmount = medicalBillInfoVO.getImmoderateAmount();
        immoderateAmount = BigDecimalUtils.convertBigDecimal(immoderateAmount);

        BigDecimal prepaidAmount = medicalBillInfoVO.getPrepaidAmount();
        prepaidAmount = BigDecimalUtils.convertBigDecimal(prepaidAmount);
        //合理金额 = 账单金额 - 自费金额 - 部分自费金额 - 不合理金额 - 第三方支付
        BigDecimal reasonableAmount = billAmount.subtract(deductibleAmount).subtract(partialDeductible).subtract(immoderateAmount)
                .subtract(prepaidAmount);
        return reasonableAmount.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : reasonableAmount;
    }


    private void updateBillReduceDetailList(List<MedicalBillReduceDetailDTO> billReduceDetailList, String userUM,
                                            String idAhcsBillInfo, Date archiveTime) {
        List<String> idAhcsBillInfoList = new ArrayList<>();
        idAhcsBillInfoList.add(idAhcsBillInfo);
        ahcsCommonService.batchHandlerTransactionalWithArgs(MedicalBillReduceDetailMapper.class, idAhcsBillInfoList, ListUtils.GROUP_NUM, REMOVE_BILL_REDUCE_DETAIL_LIST, userUM);
        if (billReduceDetailList != null && billReduceDetailList.size() > 0) {
            for (MedicalBillReduceDetailDTO billReduceDetail : billReduceDetailList) {
                billReduceDetail.setUpdatedBy(userUM);
                billReduceDetail.setIdAhcsBillInfo(idAhcsBillInfo);
                billReduceDetail.setCreatedBy(userUM);
                billReduceDetail.setArchiveTime(archiveTime);
                medicalBillReduceDetailMapper.addBillReduceDetail(billReduceDetail);
            }
        }
    }


    @Override
    public MedicalBillInfoDTO getMedicalBillAllAmount(String reportNo, Integer caseTimes, String lossObjectNo) {
        return medicalBillInfoMapper.getMedicalBillAllAmount(reportNo, caseTimes, lossObjectNo);
    }


    @Override
    public CompareBillNoResultVO getSameBillNoReportNo(MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException {

        //通过账单号查询ahcs_bill_info已存的的账单
        List<CompareBillNoResultVO> compareBillNoResultVOList = medicalBillInfoMapper.getSameBillNoReportNo(medicalBillInfoVO);
        if (ListUtils.isNotEmpty(compareBillNoResultVOList)) {
            String idAhcsBillInfo = medicalBillInfoVO.getIdAhcsBillInfo();
            boolean isChangeBillNo = true;
            CompareBillNoResultVO compareBillNoResultVO = null;
            if (StringUtils.isNotEmpty(idAhcsBillInfo)) {
                MedicalBillInfoVO medicalBill = this.getMedicalBill(idAhcsBillInfo);
                if (medicalBill != null && medicalBillInfoVO.getBillNo().equals(medicalBill.getBillNo())) {
                    StringBuffer str = new StringBuffer();
                    str.append(medicalBill.getReportNo()).append("_").append(medicalBill.getCaseTimes());
                    for (CompareBillNoResultVO compareBillNoResult : compareBillNoResultVOList) {
                        if (compareBillNoResult.getReportNo().equals(str.toString())) {
                            compareBillNoResultVO = compareBillNoResult;
                            isChangeBillNo = false;
                            break;
                        }
                    }
                }
            }
            compareBillNoResultVOList.remove(compareBillNoResultVO);
            if (ListUtils.isNotEmpty(compareBillNoResultVOList)) {
                CompareBillNoResultVO compareBillNoResult = new CompareBillNoResultVO();
                if (isChangeBillNo) {
                    CompareBillNoResultVO currentCaseSameBill = medicalBillInfoMapper.validBillExist(medicalBillInfoVO);
                    if (currentCaseSameBill != null) {
                        compareBillNoResult.setCurrentCaseSameBill(currentCaseSameBill);
                        CompareBillNoResultVO compareBillNoResultDel = null;
                        for (CompareBillNoResultVO compareBillNoResultNext : compareBillNoResultVOList) {
                            if (compareBillNoResultNext.getReportNo().equals(currentCaseSameBill.getReportNo())) {
                                compareBillNoResultDel = compareBillNoResultNext;
                                break;
                            }
                        }
                        compareBillNoResultVOList.remove(compareBillNoResultDel);
                    }
                }
                compareBillNoResult.setOtherCaseSameBillList(compareBillNoResultVOList);
                if (ListUtils.isNotEmpty(compareBillNoResultVOList)) {
                    List<String> reportNoList = compareBillNoResultVOList.stream().map(i -> i.getReportNo().substring(0, i.getReportNo().indexOf("_"))).distinct().collect(Collectors.toList());
                    reportNoList.remove(medicalBillInfoVO.getReportNo());
                    compareBillNoResult.setOtherCaseReportNoList(reportNoList);
                }
                return compareBillNoResult;
            }
        }
        return null;
    }


    private MedicalBillInfoVO needBillSpecial(MedicalBillInfoVO medicalBillInfoVO) {
        MedicalBillSpecialDTO billSpecial = medicalBillInfoVO.getBillSpecial();
        String prepaidType = medicalBillInfoVO.getPrepaidType();
        BigDecimal prepaidAmount = BigDecimalUtils.convertBigDecimal(medicalBillInfoVO.getPrepaidAmount());
        if (prepaidAmount.compareTo(BigDecimal.ZERO) > 0 && ChecklossConst.PREPAID_NEED_BILL_SPECIAL.contains(prepaidType)) {
            if (billSpecial == null) {
                billSpecial = new MedicalBillSpecialDTO();
                billSpecial.setPaymentA(medicalBillInfoVO.getReasonableAmount());
                billSpecial.setPaymentB(medicalBillInfoVO.getPartialDeductible());
                billSpecial.setIndividualPayment(medicalBillInfoVO.getDeductibleAmount());
            }
            BigDecimal personalAmount = medicalBillInfoVO.getBillAmount().subtract(BigDecimalUtils.convertBigDecimal(medicalBillInfoVO.getPrepaidAmount()));
            billSpecial.setPersonalAmount(personalAmount);
        } else {
            billSpecial = null;
        }
        medicalBillInfoVO.setBillSpecial(billSpecial);
        return medicalBillInfoVO;
    }


    private void checkSpecialBill(MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException {
        //第三方支付类型
        String prepaidType = medicalBillInfoVO.getPrepaidType();
        BigDecimal prepaidAmount = BigDecimalUtils.convertBigDecimal(medicalBillInfoVO.getPrepaidAmount());
        String idAhcsBillInfo = medicalBillInfoVO.getIdAhcsBillInfo();
        if (StringUtils.isNotEmpty(idAhcsBillInfo)) {
            MedicalBillSpecialDTO billSpecialDTO = medicalBillSpecialMapper.getBillSpecial(idAhcsBillInfo);
            if (null != billSpecialDTO) {
                medicalBillInfoVO.setPreInsuranceTotalPayment(billSpecialDTO.getInsuranceTotalPayment());
            }
        }
        if (prepaidAmount.compareTo(BigDecimal.ZERO) > 0
                && ChecklossConst.PREPAID_NEED_BILL_SPECIAL.contains(prepaidType)) {
            MedicalBillSpecialDTO billSpecial = medicalBillInfoVO.getBillSpecial();
            BigDecimal insuranceTotalPayment = null;
            if (null != billSpecial) {
                insuranceTotalPayment = billSpecial.getInsuranceTotalPayment();
            }
            //年度医疗保险基金累计支付金额是 【小于】 第三方支付金额
            if (BigDecimalUtils.compareBigDecimalAndNullMinus(insuranceTotalPayment, prepaidAmount)) {
                throw new GlobalBusinessException(ErrorCode.Checkloss.ERROR_INPUT_AMOUNT);
            }
        }
    }

    @Override
    public BigDecimal getDefaultInsuranceTotalPayment(MedicalBillInfoVO medicalBillInfoVO) throws GlobalBusinessException {

        BigDecimal preInsuranceTotalPayment = null;
        if (StringUtils.isNotEmpty(medicalBillInfoVO.getIdAhcsBillInfo())) {
            MedicalBillSpecialDTO billSpecial = medicalBillSpecialMapper.getBillSpecial(medicalBillInfoVO.getIdAhcsBillInfo());
            if (null != billSpecial) {
                preInsuranceTotalPayment = billSpecial.getInsuranceTotalPayment();
                if (ChecklossConst.NO.equals(billSpecial.getIsAutoValue())) {
                    preInsuranceTotalPayment = BigDecimalUtils.convertBigDecimal(preInsuranceTotalPayment);
                    medicalBillInfoVO.setPreInsuranceTotalPayment(preInsuranceTotalPayment);
                    return preInsuranceTotalPayment;
                }
            }
            preInsuranceTotalPayment = BigDecimalUtils.convertBigDecimal(preInsuranceTotalPayment);
            medicalBillInfoVO.setPreInsuranceTotalPayment(preInsuranceTotalPayment);
            MedicalBillInfoVO medicalBillInfo = medicalBillInfoMapper.getBillInfo(medicalBillInfoVO.getIdAhcsBillInfo());
            BigDecimal prePrepaidAmount = null;
            if (null != medicalBillInfo && ChecklossConst.PREPAID_NEED_BILL_SPECIAL.contains(medicalBillInfo.getPrepaidType())) {
                prePrepaidAmount = medicalBillInfo.getPrepaidAmount();
            }
            prePrepaidAmount = BigDecimalUtils.convertBigDecimal(prePrepaidAmount);
            BigDecimal prepaidAmount = BigDecimalUtils.convertBigDecimal(medicalBillInfoVO.getPrepaidAmount());
            BigDecimal result = prepaidAmount.subtract(prePrepaidAmount);
            preInsuranceTotalPayment = BigDecimalUtils.sum(preInsuranceTotalPayment, result);
        } else {

            preInsuranceTotalPayment = medicalBillInfoMapper.getPreInsuranceTotalPayment(medicalBillInfoVO);

            preInsuranceTotalPayment = BigDecimalUtils.sum(preInsuranceTotalPayment, medicalBillInfoVO.getPrepaidAmount());
        }
        return preInsuranceTotalPayment;
    }

    @Override
    public String checkAllBillRequired(String reportNo, Integer caseTimes) {
        List<MedicalBillInfoVO> medicalBillInfoList = medicalBillInfoMapper.getMedicalBillInfoForPrint(reportNo, caseTimes);
        if (ListUtils.isEmptyList(medicalBillInfoList)) {
            return null;
        }
        List<String> billRequiredEmptyList = new ArrayList<>();
        List<String> infoRequiredEmptyList = new ArrayList<>();
        List<String> reduceRequiredEmptyList = new ArrayList<>();
        for (MedicalBillInfoVO billInfo : medicalBillInfoList) {
            if (!ChecklossConst.BILL_TYPE_ONE.equals(billInfo.getBillClass())) {
                continue;
            }
            boolean isInfoRequiredEmpty = StringUtils.isEmpty(billInfo.getBillNo())
                    || StringUtils.isEmpty(billInfo.getHospitalName())
                    || StringUtils.isEmpty(billInfo.getTherapyType())
                    || billInfo.getStartDate() == null
                    || billInfo.getBillAmount() == null;
            if (!isInfoRequiredEmpty && ChecklossConst.AHCS_THERAPY_TWO.equals(billInfo.getTherapyType())) {
                isInfoRequiredEmpty = (billInfo.getEndDate() == null);
            }
            boolean isReduceRequiredEmpty = false;
            if (ListUtils.isNotEmpty(billInfo.getMedicalBillReduceDetailDTOList())) {
                for (MedicalBillReduceDetailDTO billReduceDetail : billInfo.getMedicalBillReduceDetailDTOList()) {
                    isReduceRequiredEmpty = StringUtils.isEmpty(billReduceDetail.getFeeName())
                            || StringUtils.isEmpty(billReduceDetail.getCostCode())
                            || StringUtils.isEmpty(billReduceDetail.getInsuranceTypeCode())
                            || billReduceDetail.getDeductionRate() == null
                            || billReduceDetail.getBillAmount() == null
                            || billReduceDetail.getDeductible() == null;
                }
            }
            if (isInfoRequiredEmpty && isReduceRequiredEmpty) {
                billRequiredEmptyList.add(billInfo.getBillNo());
            } else if (isInfoRequiredEmpty && !isReduceRequiredEmpty) {
                infoRequiredEmptyList.add(billInfo.getBillNo());
            } else if (!isInfoRequiredEmpty && isReduceRequiredEmpty) {
                reduceRequiredEmptyList.add(billInfo.getBillNo());
            } else {
            }
        }
        return this.getRequiredEmptyNoticeStr(billRequiredEmptyList, infoRequiredEmptyList, reduceRequiredEmptyList);
    }

    private String getRequiredEmptyNoticeStr(List<String> billRequiredEmptyList, List<String> infoRequiredEmptyList, List<String> reduceRequiredEmptyList) {
        String requiredEmptyStr = null;
        if (ListUtils.isNotEmpty(billRequiredEmptyList)) {
            requiredEmptyStr = ListUtils.getStringWithSeparator(billRequiredEmptyList, ",");
            requiredEmptyStr = "发票" + requiredEmptyStr + "发票字段未录入完整 且 扣费明细未录入完整，请补充后重新发送";
        }
        if (ListUtils.isNotEmpty(infoRequiredEmptyList)) {
            requiredEmptyStr = StringUtils.isNotEmpty(requiredEmptyStr) ? requiredEmptyStr + ";" : "";
            String infoRequiredEmptyStr = ListUtils.getStringWithSeparator(infoRequiredEmptyList, ",");
            requiredEmptyStr = requiredEmptyStr + "发票" + infoRequiredEmptyStr + "发票字段未录入完整 ，请补充后重新发送";
        }
        if (ListUtils.isNotEmpty(reduceRequiredEmptyList)) {
            requiredEmptyStr = StringUtils.isNotEmpty(requiredEmptyStr) ? requiredEmptyStr + ";" : "";
            String reduceRequiredEmptyStr = ListUtils.getStringWithSeparator(reduceRequiredEmptyList, ",");
            requiredEmptyStr = requiredEmptyStr + "发票" + reduceRequiredEmptyStr + "扣费明细未录入完整，请补充后重新发送";
        }
        return requiredEmptyStr;
    }

    @Override
    public MedicalBillInfoVO getMedicalBill(String idAhcsBillInfo) {
        MedicalBillInfoVO medicalBillInfoVO = medicalBillInfoMapper.getBillInfo(idAhcsBillInfo);
        if (medicalBillInfoVO != null) {
            List<MedicalBillDetailDTO> medicalBillDetailList = medicalBillInfoVO.getMedicalBillDetailDTOList();

            if (medicalBillDetailList != null && !medicalBillDetailList.isEmpty()) {
                if (medicalBillInfoVO.getBillClass().equals(ChecklossConst.BILL_TYPE_ONE)) {
                    medicalBillInfoVO.setInputMode(medicalBillDetailList.get(0).getInputMode());
                }
                //针对costComment做相应的转换
                for (MedicalBillDetailDTO medicalBillDetailDTO : medicalBillDetailList) {
                    //码值字符串转换成汉字
                    String costComment = medicalBillDetailDTO.getCostComment();
                    if ("Y".equals(medicalBillDetailDTO.getRiskLabel()) && StringUtils.isNotEmpty(costComment)) {
                        String newSplit = getNewSplit(costComment);
                        medicalBillDetailDTO.setCostComment(newSplit);
                    }
                }
            }
            List<MedicalBillReduceDetailDTO> billReduceDetailList = this.getBillReduceDetailList(idAhcsBillInfo);
            medicalBillInfoVO.setMedicalBillReduceDetailDTOList(billReduceDetailList);
            MedicalBillSpecialDTO billSpecial = this.getBillSpecial(idAhcsBillInfo);
            medicalBillInfoVO.setBillSpecial(billSpecial);
        }
        //增加诊断信息，从clms_diagnose_hospital_bill_association表获取
        List<DiagnoseHospitalBillAssociationDTO> list = diagnoseHospitalBillAssociationMapper.getDiagnoseHospitalBillAssociation(idAhcsBillInfo);
        if (ListUtils.isNotEmpty(list)) {
            WesureMedicalDiseaseDTO diseaseDTO = new WesureMedicalDiseaseDTO();
            diseaseDTO.setReportNo(medicalBillInfoVO.getReportNo());
            diseaseDTO.setCaseTimes(medicalBillInfoVO.getCaseTimes());
            List<WesureMedicalDiseaseDTO> diseaseList = Optional.ofNullable(wesureSettleMapper.getWesureMedicalDisease(diseaseDTO)).orElse(new ArrayList<>());
            if (ListUtils.isNotEmpty(diseaseList)) {
                for (DiagnoseHospitalBillAssociationDTO dto : list) {
                    String diaName = dto.getDiagnoseName();
                    if (diaName != null && diaName.length() > 0) {
                        continue;
                    }

                    dto.setDiagnoseName(diseaseList.get(0).getDiseaseName());
                    final String diaCode = dto.getDiagnoseCode();
                    if (diaCode != null && diaCode.length() > 0) {
                        String name = diseaseList.stream().filter(e -> diaCode.equals(e.getDiseaseCode())).findFirst().orElse(new WesureMedicalDiseaseDTO()).getDiseaseName();
                        if (name != null && name.length() > 0) {
                            dto.setDiagnoseName(name);
                        }
                    }
                }
            }

        }
        medicalBillInfoVO.setDiagnoseHospitalBillAssociationList(list);

        return medicalBillInfoVO;
    }

    @Override
    public String getNewSplit(String s) {
        String newSplit = "";
        List<String> list = Arrays.asList(s.split(","));
        if (list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                String name = BillEnum.getName(list.get(i));
                if (i == 0) {
                    newSplit = name;
                } else {
                    newSplit = newSplit + "," + name;
                }
            }
        }
        return newSplit;
    }

    @Transactional
    @Override
    public CompareBillNoResultVO modifyMedicalBill(MedicalBillInfoVO medicalBillInfoVO, String userUM) throws Exception {
        checkAmount(medicalBillInfoVO);
        String idAhcsBillInfo = medicalBillInfoVO.getIdAhcsBillInfo();
        String billNo = medicalBillInfoVO.getBillNo();
        if (StringUtils.isNotEmpty(billNo)) {
            MedicalBillInfoVO medicalBill = this.getMedicalBill(idAhcsBillInfo);
            if (medicalBill != null && !billNo.equals(medicalBill.getBillNo())) {
                CompareBillNoResultVO compareBillNoResultVO = medicalBillInfoMapper.validBillExist(medicalBillInfoVO);
                if (compareBillNoResultVO != null) {
                    LogUtil.audit("发票号在本案下已有保存！");
                    return compareBillNoResultVO;
                }
            } else if (medicalBill == null) {
                LogUtil.audit("该发票已失效。idAhcsBillInfo=%s", idAhcsBillInfo);
                return null;
            }
        }
//        this.checkSpecialBill(medicalBillInfoVO);
        Date archiveTime = caseProcessService.getArchiveTimeByReportNo(medicalBillInfoVO.getReportNo(), medicalBillInfoVO.getCaseTimes());
        LogUtil.audit("#是否需要保存扣费明细，reportNo=%s,saveReduceDetail=%s", medicalBillInfoVO.getReportNo(), medicalBillInfoVO.getSaveReduceDetail());
        if (StringUtils.isEmpty(medicalBillInfoVO.getSaveReduceDetail()) || ChecklossConst.TRUE.equals(medicalBillInfoVO.getSaveReduceDetail())) {
            List<MedicalBillReduceDetailDTO> billReduceDetailList = medicalBillInfoVO.getMedicalBillReduceDetailDTOList();
            this.updateBillReduceDetailList(billReduceDetailList, userUM, medicalBillInfoVO.getIdAhcsBillInfo(), archiveTime);
        }
        List<String> idAhcsBillInfoList = new ArrayList<String>();
        idAhcsBillInfoList.add(medicalBillInfoVO.getIdAhcsBillInfo());
        ahcsCommonService.batchHandlerTransactional(MedicalBillDetailMapper.class, idAhcsBillInfoList, 40, REMOVE_BILL_DETAIL_LIST, userUM);
        List<MedicalBillDetailDTO> medicalBillDetailDTOList = this.getBillDetailList(medicalBillInfoVO, userUM, archiveTime);
        ahcsCommonService.batchHandlerTransactional(MedicalBillDetailMapper.class, medicalBillDetailDTOList, 40, ADD_BILL_DETAIL_LIST, null);
        MedicalBillInfoVO medicalBillInfo = this.getMedicalInfo(medicalBillInfoVO, medicalBillDetailDTOList, userUM);
        medicalBillInfo.setArchiveTime(archiveTime);
        medicalBillInfoMapper.modifyBillInfo(medicalBillInfo);
        //this.needBillSpecial(medicalBillInfoVO);
        this.addBillSpecial(medicalBillInfoVO.getBillSpecial(), userUM, medicalBillInfo.getIdAhcsBillInfo(), archiveTime);
        //将诊断信息存入clms_diagnose_hospital_bill_association表中
        this.saveDiagnoseHospitalBillAssociation(medicalBillInfoVO.getDiagnoseHospitalBillAssociationList(), userUM, idAhcsBillInfo, medicalBillInfoVO.getReportNo());
        return null;
    }

    @Override
    public MedicalBillSpecialDTO getBillSpecial(String idAhcsBillInfo) {
        return medicalBillSpecialMapper.getBillSpecial(idAhcsBillInfo);
    }


    @Override
    public BigDecimal getAllLossAmount(String reportNo, Integer caseTimes) {
        MedicalBillInfoDTO medicalBillInfoDTO = medicalBillInfoMapper.getMedicalBillTotal(reportNo, caseTimes);
        BigDecimal billAmountSum = BigDecimal.ZERO;
        if (medicalBillInfoDTO != null) {
            billAmountSum = billAmountSum.add(BigDecimalUtils.convertBigDecimal(medicalBillInfoDTO.getBillAmount()));
        }

        String taskId = caseClassDao.getCurrentTaskCodeFromCaseClass(reportNo, caseTimes, "");

        FlightDelayDTO flightDelayDTO = flightDelayMapper.getFlightDelay(reportNo, caseTimes, "", taskId, null);
        BigDecimal flightDelayAmountSum = BigDecimal.ZERO;
        if (flightDelayDTO != null) {
            flightDelayAmountSum = flightDelayAmountSum.add(BigDecimalUtils.convertBigDecimal(flightDelayDTO.getLossAmount()));
        }

        ExaminFailDTO examinFailDTO = examinFailMapper.getExaminFail(reportNo, caseTimes, "", taskId, null);
        BigDecimal examinFailAmountSum = BigDecimal.ZERO;
        if (examinFailDTO != null) {
            examinFailAmountSum = examinFailAmountSum.add(BigDecimalUtils.convertBigDecimal(examinFailDTO.getLossAmount()));
        }

        List<TravelAlertDTO> travelAlertDTOList = travelAlertMapper.getTravelAlert(reportNo, caseTimes, "", taskId, null);
        BigDecimal travelAlertAmountSum = BigDecimal.ZERO;
        if (ListUtils.isNotEmpty(travelAlertDTOList)) {
            for (TravelAlertDTO travelAlertDTO : travelAlertDTOList) {
                travelAlertAmountSum = travelAlertAmountSum.add(BigDecimalUtils.convertBigDecimal(travelAlertDTO.getLossAmount()));
            }
        }

        List<PropertyLossDTO> propertyLossDTOList = propertyLossMapper.getPropertyLoss(reportNo, caseTimes, "", taskId, null);
        BigDecimal propertyLossAmountSum = BigDecimal.ZERO;
        if (ListUtils.isNotEmpty(propertyLossDTOList)) {
            for (PropertyLossDTO propertyLossDTO : propertyLossDTOList) {
                propertyLossAmountSum = propertyLossAmountSum.add(BigDecimalUtils.convertBigDecimal(propertyLossDTO.getLossAmount()));
            }
            BigDecimal thirdPropertyLossAmount = BigDecimalUtils
                    .convertBigDecimal(propertyLossDTOList.get(0).getThirdPropertyLossAmount());
            propertyLossAmountSum = propertyLossAmountSum.add(thirdPropertyLossAmount);
        }

        List<OtherLossDTO> otherLossDTOList = otherLossMapper.getOtherLoss(reportNo, caseTimes, taskId, null);
        BigDecimal otherLossAmountSum = BigDecimal.ZERO;
        if (otherLossDTOList != null) {
            for (OtherLossDTO otherLossDTO : otherLossDTOList) {
                otherLossAmountSum = otherLossAmountSum.add(BigDecimalUtils.convertBigDecimal(otherLossDTO.getLossAmount()));
            }
        }

        return billAmountSum.add(flightDelayAmountSum).add(examinFailAmountSum).add(travelAlertAmountSum)
                .add(propertyLossAmountSum).add(otherLossAmountSum);
    }

    @Override
    public List<MedicalBillInfoVO> getMedicalBillInfoForPrint(String reportNo, Integer caseTimes) {

        return medicalBillInfoMapper.getMedicalBillInfoForPrint(reportNo, caseTimes);

    }

    @Override
    public List<PersonDiagnoseDTO> getPersonDiagnoseList(String reportNo, Integer caseTimes) {
        List<PersonDiagnoseDTO> personDiagnoseDTOList =
                medicalBillInfoMapper.getPersonDiagnoseList(reportNo, caseTimes,null);
        return personDiagnoseDTOList;
    }

    @Override
    public List<MedicalBillInfoVO> checkBills(String reportNo, Integer caseTimes) {
        List<MedicalBillInfoVO> medicalBillInfoVOList = medicalBillInfoMapper.getMedicalBillInfoForPrint(reportNo, caseTimes);
        return medicalBillInfoVOList;
    }

    @Transactional
    public void validAndAddOuterMedicalBill(MedicalDTO medicalDTO) {
        medicalDTO.setCaseTimes(1);
        //每张账单循环校验基本参数
        for (MedicalInfoDTO medicalInfoDTO : medicalDTO.getMedicalInfos()) {
            List<MedicalBillDTO> medicalBillDTOList = medicalInfoDTO.getMedicalBillDTOList();
            for(MedicalBillDTO medicalBillDTO:medicalBillDTOList) {
                MedicalBillInfoVO medicalBillInfoVO = new MedicalBillInfoVO();
                medicalBillInfoVO.setBillAmount(medicalBillDTO.getBillAmount());
                medicalBillInfoVO.setDeductibleAmount(medicalBillDTO.getDeductibleAmount());
                medicalBillInfoVO.setPartialDeductible(medicalBillDTO.getPartialDeductible());
                medicalBillInfoVO.setPrepaidAmount(medicalBillDTO.getPrepaidAmount());
                medicalBillInfoVO.setImmoderateAmount(medicalBillDTO.getImmoderateAmount());
                checkAmount(medicalBillInfoVO);
            }
        }
        Date archiveTime = caseProcessService.getArchiveTimeByReportNo(medicalDTO.getReportNo(), medicalDTO.getCaseTimes());
        Map<String, HospitalInfoDTO> hospitalInfoDTOMap = new HashMap<>();
        //开始录入相关信息
        for(MedicalInfoDTO medicalInfoDTO : medicalDTO.getMedicalInfos()) {
            for (MedicalBillDTO medicalBillDTO : medicalInfoDTO.getMedicalBillDTOList()) {
                String idAhcsBillInfo = UuidUtil.getUUID();
                //clms_bill_info
                MedicalBillInfoVO billInfoVO = new MedicalBillInfoVO();
                BeanUtils.copyProperties(medicalBillDTO, billInfoVO);
                billInfoVO.setIdAhcsBillInfo(idAhcsBillInfo);
                billInfoVO.setReportNo(medicalDTO.getReportNo());
                billInfoVO.setIdClmChannelProcess(UuidUtil.getUUID());
                billInfoVO.setCaseTimes(1);
                BigDecimal reasonableAmount = this.getReasonableAmount(billInfoVO);
                billInfoVO.setReasonableAmount(reasonableAmount);
                billInfoVO.setArchiveTime(archiveTime);
                billInfoVO.setCreatedBy("system");
                billInfoVO.setUpdatedBy("system");
                billInfoVO.setGrade(medicalBillDTO.getHospitalGrade());
                billInfoVO.setHospitalPropertyDes(medicalBillDTO.getHospitalPropertyDes());
                medicalBillInfoMapper.addBillInfo(billInfoVO);

                if(null == hospitalInfoDTOMap.get(medicalBillDTO.getHospitalCode() + medicalBillDTO.getHospitalName())){
                    HospitalInfoDTO hospitalInfoDTO = new HospitalInfoDTO();
                    hospitalInfoDTO.setHospitalCode(medicalBillDTO.getHospitalCode());
                    hospitalInfoDTO.setHospitalName(medicalBillDTO.getHospitalName());
                    hospitalInfoDTO.setProvinceCode(medicalBillDTO.getHospitalProvinceCode());
                    hospitalInfoDTO.setPrefectureLevelCode(medicalBillDTO.getHospitalCityCode());
                    hospitalInfoDTO.setDistrictCode(medicalBillDTO.getHospitalDistrictCode());
                    hospitalInfoDTO.setAddressDesc(medicalBillDTO.getVisitPlace());
                    hospitalInfoDTO.setGrade(medicalBillDTO.getHospitalGrade());
                    hospitalInfoDTO.setHospitalType(medicalBillDTO.getHospitalPropertyDes());
                    hospitalInfoDTOMap.put(medicalBillDTO.getHospitalCode() + medicalBillDTO.getHospitalName(), hospitalInfoDTO);
                }

                //clms_bill_reduce_detail 账单费用扣费明细表 会有多条数据
                if(ListUtils.isNotEmpty(medicalBillDTO.getMedicalBillReduceDTOList())) {
                    MedicalBillReduceDetailDTO billReduceDetailDTO = new MedicalBillReduceDetailDTO();
                    List<MedicalBillReduceDTO> medicalBillReduceDTOList = medicalBillDTO.getMedicalBillReduceDTOList();
                    for (MedicalBillReduceDTO billReduceDTO : medicalBillReduceDTOList) {
                        BeanUtils.copyProperties(billReduceDTO, billReduceDetailDTO);
                        billReduceDetailDTO.setIdAhcsBillReduceDetail(UuidUtil.getUUID());
                        billReduceDetailDTO.setIdAhcsBillInfo(idAhcsBillInfo);
                        billReduceDetailDTO.setArchiveTime(archiveTime);
                        billReduceDetailDTO.setCreatedBy("system");
                        billReduceDetailDTO.setUpdatedBy("system");
                        medicalBillReduceDetailMapper.addBillReduceDetail(billReduceDetailDTO);
                    }
                }


                //clms_bill_detail
                MedicalBillDetailDTO medicalBillDetailDTO = new MedicalBillDetailDTO();
                BeanUtils.copyProperties(medicalBillDTO, medicalBillDetailDTO);
                medicalBillDetailDTO.setIdAhcsBillDetail(UuidUtil.getUUID());
                medicalBillDetailDTO.setIdAhcsBillInfo(idAhcsBillInfo);
                medicalBillDetailDTO.setArchiveTime(archiveTime);
                medicalBillDetailDTO.setCreatedBy("system");
                medicalBillDetailDTO.setUpdatedBy("system");
                medicalBillDetailDTO.setCostComment(medicalBillDTO.getRemark());
                medicalBillDetailMapper.addBillDetail(medicalBillDetailDTO);

                //clms_bill_special
                MedicalBillSpecialDTO billSpecial = new MedicalBillSpecialDTO();
                billSpecial.setIdAhcsBillSpecial(UuidUtil.getUUID());
                billSpecial.setUpdatedBy(WebServletContext.getUserId());
                billSpecial.setIdAhcsBillInfo(idAhcsBillInfo);
                billSpecial.setCreatedBy(WebServletContext.getUserId());
                billSpecial.setArchiveTime(archiveTime);
                billSpecial.setCreatedBy("system");
                billSpecial.setUpdatedBy("system");
                medicalBillSpecialMapper.addBillSpecial(billSpecial);
                //clms_diagnose_hospital_bill_association
                for (Diagnose diagnose : medicalInfoDTO.getDiagnoseList()) {
                    DiagnoseHospitalBillAssociationDTO billAssociationDTO = new DiagnoseHospitalBillAssociationDTO();
                    billAssociationDTO.setIdClmsDiagnoseHospitalBillAssociation(UuidUtil.getUUID());
                    billAssociationDTO.setIdClmsBillInfo(idAhcsBillInfo);
                    billAssociationDTO.setDiagnoseCode(diagnose.getDiagnoseCode());
                    billAssociationDTO.setDiagnoseName(diagnose.getDiagnoseName());
                    billAssociationDTO.setCreatedBy("system");
                    billAssociationDTO.setUpdatedBy("system");
                    billAssociationDTO.setIsMain(diagnose.getIsMain());
                    diagnoseHospitalBillAssociationMapper.saveDiagnoseHospitalBillAssociation(billAssociationDTO);
                }
            }
        }
        hospitalInfoPlusService.hospitalInfoUpdateByCase(hospitalInfoDTOMap, medicalDTO.getReportNo());
    }

    @Transactional
    public void validAndAddTpaReceipt(MedicalDTO medicalDTO) {
        medicalDTO.setCaseTimes(1);
        //每张账单循环校验基本参数
        for (MedicalInfoDTO medicalInfoDTO : medicalDTO.getMedicalInfos()) {
            List<MedicalBillDTO> medicalBillDTOList = medicalInfoDTO.getMedicalBillDTOList();
            for(MedicalBillDTO medicalBillDTO:medicalBillDTOList) {
                MedicalBillInfoVO medicalBillInfoVO = new MedicalBillInfoVO();
                medicalBillInfoVO.setBillAmount(medicalBillDTO.getBillAmount());
                medicalBillInfoVO.setDeductibleAmount(medicalBillDTO.getDeductibleAmount());
                medicalBillInfoVO.setPartialDeductible(medicalBillDTO.getPartialDeductible());
                medicalBillInfoVO.setPrepaidAmount(medicalBillDTO.getPrepaidAmount());
                medicalBillInfoVO.setImmoderateAmount(medicalBillDTO.getImmoderateAmount());
                checkAmount(medicalBillInfoVO);
            }
        }
        Date archiveTime = caseProcessService.getArchiveTimeByReportNo(medicalDTO.getReportNo(), medicalDTO.getCaseTimes());
        //开始录入相关信息
        for(MedicalInfoDTO medicalInfoDTO : medicalDTO.getMedicalInfos()) {
            for (MedicalBillDTO medicalBillDTO : medicalInfoDTO.getMedicalBillDTOList()) {
                String idAhcsBillInfo = UuidUtil.getUUID();
                String idAhcsBillDetail = UuidUtil.getUUID();
                //clms_bill_info_Amend
                MedicalBillInfoVO billInfoVO = new MedicalBillInfoVO();
                BeanUtils.copyProperties(medicalBillDTO, billInfoVO);
                billInfoVO.setIdAhcsBillInfo(idAhcsBillInfo);
                billInfoVO.setReportNo(medicalDTO.getReportNo());
                billInfoVO.setIdClmChannelProcess(UuidUtil.getUUID());
                billInfoVO.setCaseTimes(1);
                BigDecimal reasonableAmount = this.getReasonableAmount(billInfoVO);
                billInfoVO.setReasonableAmount(reasonableAmount);
                billInfoVO.setArchiveTime(archiveTime);
                billInfoVO.setCreatedBy("system");
                billInfoVO.setUpdatedBy("system");
                billInfoVO.setGrade(medicalBillDTO.getHospitalGrade());
                billInfoVO.setHospitalPropertyDes(medicalBillDTO.getHospitalPropertyDes());
                medicalBillInfoMapper.addBillInfoAmend(billInfoVO);

                //clms_bill_reduce_detail_Amend 账单费用扣费明细表 会有多条数据
                MedicalBillReduceDetailDTO billReduceDetailDTO = new MedicalBillReduceDetailDTO();
                List<MedicalBillReduceDTO> medicalBillReduceDTOList = medicalBillDTO.getMedicalBillReduceDTOList();
                for (MedicalBillReduceDTO billReduceDTO : medicalBillReduceDTOList) {
                    BeanUtils.copyProperties(billReduceDTO, billReduceDetailDTO);
                    billReduceDetailDTO.setIdAhcsBillReduceDetail(UuidUtil.getUUID());
                    billReduceDetailDTO.setIdAhcsBillInfo(idAhcsBillInfo);
                    billReduceDetailDTO.setIdAhcsBillDetail(idAhcsBillDetail);
                    billReduceDetailDTO.setArchiveTime(archiveTime);
                    billReduceDetailDTO.setCreatedBy("system");
                    billReduceDetailDTO.setUpdatedBy("system");
                    billReduceDetailDTO.setFeeName(billReduceDTO.getItemName());
                    medicalBillReduceDetailMapper.addBillReduceDetailAmend(billReduceDetailDTO);
                }

                //clms_bill_detail_Amend
                MedicalBillDetailDTO medicalBillDetailDTO = new MedicalBillDetailDTO();
                BeanUtils.copyProperties(medicalBillDTO, medicalBillDetailDTO);
                medicalBillDetailDTO.setIdAhcsBillDetail(idAhcsBillDetail);
                medicalBillDetailDTO.setIdAhcsBillInfo(idAhcsBillInfo);
                medicalBillDetailDTO.setArchiveTime(archiveTime);
                medicalBillDetailDTO.setCreatedBy("system");
                medicalBillDetailDTO.setUpdatedBy("system");
                medicalBillDetailDTO.setCostComment(medicalBillDTO.getRemark());
                medicalBillDetailMapper.addBillDetailAmend(medicalBillDetailDTO);
            }
        }
    }

    public void checkParameter(MedicalDTO medicalDTO) {
        if (StringUtils.isEmpty(medicalDTO.getReportNo())) {
            throw new GlobalBusinessException("报案号不能为空");
        }
        for(MedicalInfoDTO medicalInfos:medicalDTO.getMedicalInfos()) {
            if (CollectionUtils.isEmpty(medicalInfos.getMedicalBillDTOList())) {
                throw new GlobalBusinessException("发票信息不能为空");
            }
            for (MedicalBillDTO billdto : medicalInfos.getMedicalBillDTOList()) {
                if (StringUtils.isEmpty(billdto.getBillNo())) {
                    throw new GlobalBusinessException("发票号不能为空");
                }
                if (StringUtils.isEmpty(billdto.getBillClass())) {
                    throw new GlobalBusinessException("账单类型不能为空");
                }
                if (StringUtils.isEmpty(billdto.getBillType())) {
                    throw new GlobalBusinessException("发票类型不能为空");
                }
                if (StringUtils.isEmpty(billdto.getBillCategory())) {
                    throw new GlobalBusinessException("发票种类不能为空");
                }
                if (StringUtils.isEmpty(billdto.getTherapyType())) {
                    throw new GlobalBusinessException("就诊类型不能为空");
                }
                if (Objects.isNull(billdto.getStartDate())) {
                    throw new GlobalBusinessException("发票日期不能为空");
                }
                if (StringUtils.isEmpty(billdto.getHospitalCode())) {
                    throw new GlobalBusinessException("医院编码不能为空");
                }
                if (StringUtils.isEmpty(billdto.getHospitalName())) {
                    throw new GlobalBusinessException("医院名称不能为空");
                }
//                if(StringUtils.isEmpty(billdto.getHospitalGrade())) {
//                    throw new GlobalBusinessException("医院等级不能为空");
//                }
//                if(StringUtils.isEmpty(billdto.getHospitalPropertyDes())) {
//                    throw new GlobalBusinessException("医院性质不能为空");
//                }
                if (Objects.isNull(billdto.getBillAmount())) {
                    throw new GlobalBusinessException("账单金额不能为空");
                }
                //判断是否是tpa的数据
                ReportInfoExEntity reportInfoExEntity = reportInfoExMapper.getAcceptanceNoByReportNo(medicalDTO.getReportNo());
                if("42300010".equals(reportInfoExEntity.getCompanyId())){
                    if (Objects.isNull(billdto.getHospitalProvinceCode())) {
                        throw new GlobalBusinessException("医院所在省编码不能为空");
                    }
                    if (Objects.isNull(billdto.getHospitalProvinceName())) {
                        throw new GlobalBusinessException("医院所在省名称不能为空");
                    }
                    if (Objects.isNull(billdto.getHospitalCityCode())) {
                        throw new GlobalBusinessException("医院所在市编码不能为空");
                    }
                    if (Objects.isNull(billdto.getHospitalCityName())) {
                        throw new GlobalBusinessException("医院所在市名称不能为空");
                    }
                    if (Objects.isNull(billdto.getHospitalDistrictCode())) {
                        throw new GlobalBusinessException("医院所在区县编码不能为空");
                    }
                    if (Objects.isNull(billdto.getHospitalDistrictName())) {
                        throw new GlobalBusinessException("医院所在区县名称不能为空");
                    }
                }
            }
        }

//        TaskInfoDTO taskInfoDTO = taskInfoService.checkWorkflow(medicalDTO.getReportNo(), 1, BpmConstants.OC_CHECK_DUTY, "0");
//        if (taskInfoDTO == null) {
//            throw new GlobalBusinessException(ErrorCode.MedicalBill.FAIL_MEDICALBILL_WORKFLOW, "工作流不在当前节点");
//        }
//        List<MedicalBillInfoVO> medicalBillInfoVOS = checkBills(medicalDTO.getReportNo(), medicalDTO.getCaseTimes());
//        if (medicalBillInfoVOS.size() > 0) {
//            throw new GlobalBusinessException(ErrorCode.MedicalBill.FAIL_MEDICALBILL_EXISTS, "业务人员已录入账单，请勿重复操作");
//        }
    }

    @Transactional
    @Override
    public DutySurveyVO addOuterMedicalBill(MedicalDTO medicalDTO) throws Exception {
        String querytaskId = "reportTrack";
        //根据案件状态判断：理算退回收单，需删除理算数据
        if("1".equals(medicalDTO.getClaimDealWay()) && taskInfoService.checkWorkflow(medicalDTO.getReportNo(), 1, BpmConstants.OC_MANUAL_SETTLE, "0") != null) {
            try {
                //删除领款人数据
                this.rollBackPayment(medicalDTO);
                //删除收单数据
                this.rollBackCheckDuty(medicalDTO);
                //删除理算数据
                this.rollBackSettle(medicalDTO);
                //删除工作流数据
                this.delTaskInfo(medicalDTO);

                querytaskId = "checkDuty";
            } catch (Exception e) {
                throw new GlobalBusinessException("报案号：{}理算收单数据回退失败！",medicalDTO.getReportNo());
            }

        }
        //录入领款人信息
        if(CollectionUtils.isNotEmpty(medicalDTO.getPaymentList())) {
            if("1".equals(medicalDTO.getClaimDealWay()) && taskInfoService.checkWorkflow(medicalDTO.getReportNo(), 1, BpmConstants.OC_CHECK_DUTY, "0") != null) {
                this.rollBackPayment(medicalDTO);
            }
            savePayMent(medicalDTO.getPaymentList(),medicalDTO.getReportNo());
            saveClmsAntiMoneyInfo(medicalDTO.getPaymentList(),medicalDTO.getReportNo());
        }
        //1、根据报案号查询账单相关表是否录入数据，如果有则代表业务操作过则不再继续（完成）
        this.checkParameter(medicalDTO);
        //2、使用该接口的入参借鉴新增账单方法，将相关单证信息补充完整
        this.validAndAddOuterMedicalBill(medicalDTO);
        //保存收单数据
        DutySurveyVO dutyVO = getDutySurveyVO(medicalDTO, querytaskId);
        dutySurveyService.handleDutySurvey(dutyVO);
        return dutyVO;
        //3、流转工作流到理算环节（完成）
        //dutySurveyService.flowWorkFlow(medicalDTO);
    }

    private void saveClmsAntiMoneyInfo(List<PaymentInfoVO> paymentInfoList, String reportNo) {
        try {
            if(!paymentInfoList.isEmpty()){
                //历史的反洗钱信息置为失效
                ClmsAntiMoneyLaunderingInfoDto invalidClmsAntiMoneyDto = new ClmsAntiMoneyLaunderingInfoDto();
                invalidClmsAntiMoneyDto.setReportNo(reportNo);
                invalidClmsAntiMoneyDto.setCaseTimes(Integer.valueOf(ReportConstant.INIT_CASE_TIMES));
                antiMoneyLaunderingMapper.invalidClmsAntiMoney(invalidClmsAntiMoneyDto);
                for(PaymentInfoVO paymentInfoVO :paymentInfoList){
                    if("1".equals(paymentInfoVO.getBankAccountAttribute())){
                        //小于1W的反洗钱信息不需要传，但是反洗钱字段和领款人是同一个LIST，所以要判断反洗钱信息字段是否有值
                        if(checkIsEmpty(paymentInfoVO)){
                            //反洗钱相关信息收集
                            ClmsAntiMoneyLaunderingInfoDto clmsAntiMoneyLaunderingInfoDto = new ClmsAntiMoneyLaunderingInfoDto();
                            BeanUtils.copyProperties(paymentInfoVO,clmsAntiMoneyLaunderingInfoDto);
                            clmsAntiMoneyLaunderingInfoDto.setReportNo(reportNo);
                            clmsAntiMoneyLaunderingInfoDto.setCaseTimes(Integer.valueOf(ReportConstant.INIT_CASE_TIMES));
                            clmsAntiMoneyLaunderingInfoDto.setCreatedBy("system");
                            clmsAntiMoneyLaunderingInfoDto.setUpdatedBy("system");
                            PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
                            paymentInfoDTO.setClientName(paymentInfoVO.getClientName());
                            paymentInfoDTO.setBankAccountAttribute(paymentInfoVO.getBankAccountAttribute());
                            paymentInfoDTO.setClientCertificateType(paymentInfoVO.getClientCertificateType());
                            paymentInfoDTO.setClientCertificateNo(paymentInfoVO.getClientCertificateNo());
                            paymentInfoService.getCustomerNo(paymentInfoDTO);
                            clmsAntiMoneyLaunderingInfoDto.setCustomerNo(paymentInfoDTO.getCustomerNo());
                            antiMoneyLaunderingMapper.addClmsAntiMoneyLaunderingInfo(clmsAntiMoneyLaunderingInfoDto);
                        }
                    }
                }
            }
        }catch (Exception e){
            LogUtil.error("tpa回传收款人信息新增反洗钱信息字段失败，案件号：{},msg={}", reportNo,e.getMessage(),e);
        }

    }

    public Boolean checkIsEmpty(PaymentInfoVO paymentInfoVO){
        return Stream.of("AreaCode", "NationCode", "ProfessionMaximumCode", "ProfessionMediumCode",
                        "ProfessionMinimumCode", "CertificateEffectiveDate", "CertificateExpireDate",
                        "Gender", "IncomeCode", "IncomeText", "Company", "Address")
                .anyMatch(fieldName -> {
                    try {
                        Method getter = paymentInfoVO.getClass().getMethod("get" + fieldName);
                        String value = (String) getter.invoke(paymentInfoVO);
                        return StringUtils.isNotBlank(value);
                    } catch (Exception e) {
                        return false; // 忽略异常，视为无值
                    }
                });
    }

    @Transactional
    @Override
    public void addTpaReceipt(MedicalDTO medicalDTO) {
        //1、根据报案号查询账单相关表是否录入数据，如果有则代表业务操作过则不再继续（完成）
        this.checkParameter(medicalDTO);
        //2、使用该接口的入参借鉴新增账单方法，将相关单证信息补充完整
        this.validAndAddTpaReceipt(medicalDTO);
    }


    private void delTaskInfo(MedicalDTO medicalDTO) {
        //删除理算、收单工作流，退回至报案跟踪
        caseProcessService.updateCaseProcess(medicalDTO.getReportNo(), 1, CaseProcessStatus.PENDING_ACCEPT.getCode());
//        bpmService.suspendOrActiveTask_oc(medicalDTO.getReportNo(),1,BpmConstants.OC_MANUAL_SETTLE,false);
        TaskInfoDTO endTask = new TaskInfoDTO();
        endTask.setReportNo(medicalDTO.getReportNo());
        endTask.setCaseTimes(1);
        endTask.setTaskDefinitionBpmKey(BpmConstants.OC_MANUAL_SETTLE);
        taskInfoService.deleteTaskInfo(endTask);
        bpmService.newSuspendOrActiveTask_oc(medicalDTO.getReportNo(),1,BpmConstants.OC_CHECK_DUTY,true);
    }

    private void rollBackPayment(MedicalDTO medicalDTO) {
        PaymentInfoDTO paymentInfoDTO =new PaymentInfoDTO();
        paymentInfoDTO.setReportNo(medicalDTO.getReportNo());
        paymentInfoDTO.setCaseTimes(1);
        List<PaymentInfoDTO> paymentInfoDTOS = paymentInfoService.getPaymentInfo(paymentInfoDTO);
        //置为失效
        for(PaymentInfoDTO paymentInfo : paymentInfoDTOS) {
            PaymentInfoVO paymentInfoVO = new PaymentInfoVO();
            BeanUtils.copyProperties(paymentInfo,paymentInfoVO);
            paymentInfoService.delPaymentInfo(paymentInfoVO);
        }
    }

    private void rollBackSettle(MedicalDTO medicalDTO) {
        //删除理算数据涉及的表：
        // CLM_POLICY_BATCH_PAY
        policyBatchMapper.deleteByReportNo(medicalDTO.getReportNo(),1);
        //CLM_POLICY_PAY
        //CLM_PLAN_PAY
        //CLM_PLAN_DUTY_PAY
        //CLMS_DUTY_DETAIL_PAY
        //CLMS_ENDORSEMENT
        //CLM_PAYMENT_ITEM
        policyPayService.deletePolicyPays(medicalDTO.getReportNo(),1);
        //CLMS_LOSS_REDUCE
        LossReduceDTO lossReduceDTO = new LossReduceDTO();
        lossReduceDTO.setReportNo(medicalDTO.getReportNo());
        lossReduceDTO.setCaseTimes("1");
        lossReduceDTO.setUpdatedBy("SYSTEM");
        lossReduceMapper.updateEffective(lossReduceDTO);
        //CLMS_VERIFY
        VerifyDTO verifyDTO = new VerifyDTO();
        verifyDTO.setReportNo(medicalDTO.getReportNo());
        verifyDTO.setCaseTimes(1);
        verifyDTO.setUpdatedBy("SYSTEM");
        verifyMapper.deleteVerify(verifyDTO);
        //clms_duty_bill_limit
        dutyBillLimitInfoService.deleteByReportNoAndCaseTimes(medicalDTO.getReportNo(),1);
    }

    private void rollBackCheckDuty(MedicalDTO medicalDTO) throws Exception {
        List<String> idBillInfoList = medicalBillInfoMapper.getIdBillInfoList(medicalDTO.getReportNo(), 1);
        IdAhcsBillInfoListVO billInfoList = new IdAhcsBillInfoListVO();
        billInfoList.setIdAhcsBillInfoList(idBillInfoList);
        this.removeMedicalBill(billInfoList,"SYSTEM");
    }

    /**
     * 保存诊断信息至关联表中
     *
     * @param diagnoseHospitalBillAssociationList
     * @param userUM
     * @param idAhcsBillInfo
     * @param reportNo
     */
    private void saveDiagnoseHospitalBillAssociation(List<DiagnoseHospitalBillAssociationDTO> diagnoseHospitalBillAssociationList, String userUM, String idAhcsBillInfo, String reportNo) {
        //先将表中历史数据IS_EFFECTIVE置为无效，如果有
        diagnoseHospitalBillAssociationMapper.updateDiagnoseHospitalBillAssociation(idAhcsBillInfo);
        //循环插入数据
        if (CollectionUtils.isEmpty(diagnoseHospitalBillAssociationList)) {
            return;
        }
        for (DiagnoseHospitalBillAssociationDTO dto : diagnoseHospitalBillAssociationList) {
            dto.setIdClmsDiagnoseHospitalBillAssociation(UuidUtil.getUUID());
            dto.setIdClmsBillInfo(idAhcsBillInfo);
            dto.setCreatedBy(userUM);
            dto.setUpdatedBy(userUM);
            if (!ConstValues.YES.equals(dto.getIsSurgical())) {
                dto.setSurgicalType("");
                dto.setSurgicalName("");
            } else {
                String surgicalCode = dto.getSurgicalCode();
                String surgicalName = dto.getSurgicalName();
                if (StringUtils.isNotEmpty(surgicalCode) && StringUtils.isEmpty(surgicalName)) {
                    //根据报案号查询机构编码
                    String orgTypeByReportNo = hospitalInfoService.getOrgTypeByReportNo(reportNo, NcbsConstant.OPERATION);
                    String operationName = operationDefineMapper.getTherapyOperationByCode(surgicalCode, orgTypeByReportNo);
                    dto.setSurgicalName(operationName);
                }
            }
            if("是".equals(dto.getIsMain())){
                dto.setIsMain("Y");
            } else {
                dto.setIsMain("N");
            }
            diagnoseHospitalBillAssociationMapper.saveDiagnoseHospitalBillAssociation(dto);
        }
    }

    /**
     * 组装收单数据
     *
     * @param medicalDTO
     * @param querytaskId
     * @return
     */
    private DutySurveyVO getDutySurveyVO(MedicalDTO medicalDTO, String querytaskId) {

        String status = "1";
        String reportNo = medicalDTO.getReportNo();
        Integer caseTimes = medicalDTO.getCaseTimes();
        DutySurveyVO dutySurveyVO = new DutySurveyVO();
        dutySurveyVO.setIsSettle("N");
        dutySurveyVO.setReportNo(reportNo);
        dutySurveyVO.setCaseTimes(caseTimes);
        dutySurveyVO.setIsHugeAccident("N");
        dutySurveyVO.setTaskId("checkDuty");
        dutySurveyVO.setStatus(status);
        VerifyConclusionVO verifyConclusionVO = new VerifyConclusionVO();
        verifyConclusionVO.setAuditingCommont("TPA收单");
        verifyConclusionVO.setIndemnityConclusion("1");
        dutySurveyVO.setVerifyConclusionVO(verifyConclusionVO);
        List<String> caseClassList = caseClassDao.getCaseClassNameListCode(reportNo, caseTimes, querytaskId, status);
        dutySurveyVO.setCaseSubClass(caseClassList);
        //人伤信息
        PeopleHurtVO peopleHurtVO = new PeopleHurtVO();
        //诊断信息   
        //更新为收单的疾病信息
        String idAhcsChannelProcess = channelProcessService.getChannelProcessId(reportNo, caseTimes);
        List<PersonDiagnoseDTO> diagnoseDTOS = new ArrayList<>();
//                personDiagnoseDao.getPersonDiagnoseDTOList(idAhcsChannelProcess, querytaskId, status);
        List<PersonHospitalDTO> hospitalInfoVOLists = new ArrayList<>();
                medicalDTO.getMedicalInfos().stream().forEach(medicalInfoDTO -> {
            medicalInfoDTO.getDiagnoseList().forEach(diagnoseDTO -> {
                PersonDiagnoseDTO personDiagnoseDTO = new PersonDiagnoseDTO();
                personDiagnoseDTO.setDiagnoseCode(diagnoseDTO.getDiagnoseCode());
                personDiagnoseDTO.setDiagnoseName(diagnoseDTO.getDiagnoseName());
                personDiagnoseDTO.setIsMain(diagnoseDTO.getIsMain());
                diagnoseDTOS.add(personDiagnoseDTO);
            });
            medicalInfoDTO.getMedicalBillDTOList().forEach(hospitalDTO -> {
                //MEDICAL_STATUS medicalStatus,HOSPITAL_GRADE hospitalGrade,
                // THERAPY_TYPE therapyType,HOSPITAL_NAME hospitalName,
                // HOSPITAL_PROPERTY_DES hospitalPropertyDes
                PersonHospitalDTO personHospitalDTO = new PersonHospitalDTO();
                personHospitalDTO.setHospitalCode(hospitalDTO.getHospitalCode());
                personHospitalDTO.setHospitalName(hospitalDTO.getHospitalName());
                personHospitalDTO.setHospitalGrade(hospitalDTO.getHospitalGrade());
                personHospitalDTO.setTherapyType(hospitalDTO.getTherapyType());
                personHospitalDTO.setHospitalPropertyDes(hospitalDTO.getHospitalPropertyDes());
                personHospitalDTO.setMedicalStatus("MS_2203");
                hospitalInfoVOLists.add(personHospitalDTO);
            });
        });
        setDiagnoseDtos(diagnoseDTOS);//设置创建时间
        PersonDiagnoseVO personDiagnoseVO = new PersonDiagnoseVO();
        personDiagnoseVO.setDiagnoseDTOs(diagnoseDTOS);
        //诊断信息
        peopleHurtVO.setDiagnoseVO(personDiagnoseVO);
        //疾病信息
        PersonDiseaseDTO personDiseaseDTO = personDiseaseDao.getPersonDisease(idAhcsChannelProcess, querytaskId, status);
        setPersonDisease(personDiseaseDTO);//设置创建时间
        PersonDiseaseVO diseaseVO = new PersonDiseaseVO();
        if (personDiseaseDTO != null) {
            BeanUtils.copyProperties(personDiseaseDTO, diseaseVO);
        }
        peopleHurtVO.setDiseaseVO(diseaseVO);
        //事故信息
        PersonAccidentDTO accidentDTO = personAccidentDao.getPersonAccident(idAhcsChannelProcess, querytaskId, status);
        PersonAccidentVO accidentVO = new PersonAccidentVO();
        if (accidentDTO != null) {
            BeanUtils.copyProperties(accidentDTO, accidentVO);

            accidentVO.setAccidentProvince(accidentDTO.getProvinceCode());
            accidentVO.setAccidentCity(accidentDTO.getAccidentCityCode());
            accidentVO.setAccidentCounty(accidentDTO.getAccidentCountyCode());
        }
        peopleHurtVO.setAccidentVO(accidentVO);

//        List<PersonHospitalDTO> hospitalInfoVOLists = hospitalInfoMapper.getHostpitalvo(idAhcsChannelProcess, querytaskId, status);
//        medicalDTO.getMedicalInfos().stream().forEach(medicalInfoDTO -> {
//            medicalInfoDTO.getMedicalBillDTOList().forEach(hospitalDTO -> {
//                //MEDICAL_STATUS medicalStatus,HOSPITAL_GRADE hospitalGrade,
//                // THERAPY_TYPE therapyType,HOSPITAL_NAME hospitalName,
//                // HOSPITAL_PROPERTY_DES hospitalPropertyDes
//                PersonHospitalDTO personHospitalDTO = new PersonHospitalDTO();
//                personHospitalDTO.setHospitalCode(hospitalDTO.getHospitalCode());
//                personHospitalDTO.setHospitalName(hospitalDTO.getHospitalName());
//                personHospitalDTO.setHospitalGrade(hospitalDTO.getHospitalGrade());
//                personHospitalDTO.setTherapyType(hospitalDTO.getTherapyType());
//                personHospitalDTO.setHospitalPropertyDes(hospitalDTO.getHospitalPropertyDes());
//                hospitalInfoVOLists.add(personHospitalDTO);
//            });
//        });
        PersonHospitalVO hospitalVO = new PersonHospitalVO();
        hospitalVO.setPersonHospitalList(hospitalInfoVOLists);
        peopleHurtVO.setHospitalVO(hospitalVO);

        dutySurveyVO.setPeopleHurtVO(peopleHurtVO);

        SurveyVO surveyVO = surveyMapper.getsureyDeatalInfo(reportNo, querytaskId, status);
        dutySurveyVO.setSurveyVO(surveyVO);

        return dutySurveyVO;
    }

    /**
     * 设置创建时间
     *
     * @param personDiseaseDTO
     */
    private void setPersonDisease(PersonDiseaseDTO personDiseaseDTO) {
        if (Objects.isNull(personDiseaseDTO)) {
            return;
        }
        Date date = new Date();
        personDiseaseDTO.setCreatedDate(date);
        personDiseaseDTO.setUpdatedDate(date);
    }

    /**
     * 设置创建时间
     *
     * @param diagnoseDTOS
     */
    private void setDiagnoseDtos(List<PersonDiagnoseDTO> diagnoseDTOS) {
        if (CollectionUtils.isEmpty(diagnoseDTOS)) {
            return;
        }
        Date date = new Date();
        for (PersonDiagnoseDTO dto : diagnoseDTOS) {
            dto.setCreatedDate(date);
            dto.setUpdatedDate(date);
        }
    }

    @Override
    public void syncBillInfo(SyncBillParamDTO syncBillParamDTO) {
        String offsetDateStr = wesureMedicalBillOffSet ? "20240321" : "20240312";
        WesureMedicalDTO wesureMedicalDTO = new WesureMedicalDTO();
        wesureMedicalDTO.setReportNo(syncBillParamDTO.getReportNo());
        wesureMedicalDTO.setCaseTimes(syncBillParamDTO.getCaseTimes());
        wesureMedicalDTO.setCreatedDate(DateUtils.formatStringToDate(offsetDateStr, DateUtils.DATE_FORMAT_YYYYMMDD));
        List<WesureMedicalDTO> wesureMedicalList = wesureSettleMapper.getWesureMedicalList(wesureMedicalDTO);
        if (ListUtils.isEmptyList(wesureMedicalList)) {
            throw new GlobalBusinessException("微保账单为空");
        }

        Set<String> billNoSet = new HashSet<>();
        if (!ConfigConstValues.YES.equals(syncBillParamDTO.getConfirm())) {
            MedicalBillInfoDTO medicalBillInfoDTO = new MedicalBillInfoDTO();
            medicalBillInfoDTO.setReportNo(syncBillParamDTO.getReportNo());
            List<MedicalBillInfoDTO> medicalBillList = medicalBillInfoMapper.getBillInfoByPage(medicalBillInfoDTO);
            if (ListUtils.isNotEmpty(medicalBillList)) {
                billNoSet = medicalBillList.stream().map(e -> e.getBillNo()).collect(Collectors.toSet());
            }
        }

        for (MedicalBillInfoVO medicalBillInfoVO : wesureMedicalToBill(syncBillParamDTO, wesureMedicalList, billNoSet)) {
            validAndAddMedicalBill(medicalBillInfoVO, WebServletContext.getUserId());
        }
    }

    private List<MedicalBillInfoVO> wesureMedicalToBill(SyncBillParamDTO syncBillParamDTO, List<WesureMedicalDTO> wesureMedicalList, Set<String> billNoSet) {
        WesureMedicalDiseaseDTO diseaseDTO = new WesureMedicalDiseaseDTO();
        diseaseDTO.setReportNo(syncBillParamDTO.getReportNo());
        diseaseDTO.setCaseTimes(syncBillParamDTO.getCaseTimes());
        List<WesureMedicalDiseaseDTO> diseaseList = Optional.ofNullable(wesureSettleMapper.getWesureMedicalDisease(diseaseDTO)).orElse(new ArrayList<>());
        List<MedicalBillInfoVO> billList = new ArrayList<>();
        MedicalBillInfoVO bill;
        Set<String> repeatNoSet = new HashSet<>();
        for (WesureMedicalDTO wesureMedical : wesureMedicalList) {
            if (ListUtils.isEmptyList(wesureMedical.getReceiptList())) {
                continue;
            }
            String starDate = wesureMedical.getInHospitalDate();
            String endDate = wesureMedical.getOutHospitalDate();
            if ("THE_0301".equals(wesureMedical.getVisitType()) || "OUTPATIENT".equals(wesureMedical.getVisitType())) {
                starDate = wesureMedical.getOutpatientDate();
            }

            for (WesureMedicalReceiptDTO receipt : wesureMedical.getReceiptList()) {
                String billNo = receipt.getReceiptNo();
                if (billNoSet.contains(billNo)) {
                    repeatNoSet.add(billNo);
                }
                if (StringUtils.isEmpty(receipt.getInputMode())) {
                    continue;
                }
                bill = new MedicalBillInfoVO();
                BeanUtils.copyProperties(receipt, bill);
                bill.setReportNo(syncBillParamDTO.getReportNo());
                bill.setCaseTimes(syncBillParamDTO.getCaseTimes());
                if (receipt.getHospitalGrade() == null || receipt.getHospitalGrade() == 0) {
                    bill.setGrade("无等级");
                } else {
                    bill.setGrade(String.valueOf(receipt.getHospitalGrade()));
                }
                bill.setStartDate(DateUtils.formatStringToDate(starDate, DateUtils.DATE_FORMAT_YYYYMMDD));
                bill.setEndDate(DateUtils.formatStringToDate(endDate, DateUtils.DATE_FORMAT_YYYYMMDD));
                bill.setTherapyType(wesureMedical.getVisitType());
                String billCategory = "";
                if("E".equals(receipt.getReceiptType())){
                    billCategory = "3"; // 电子
                } else if("P".equals(receipt.getReceiptType())){
                    billCategory = "4"; // 纸质
                }
                bill.setBillCategory(billCategory);
                bill.setBillAmount(nvl(receipt.getTotalPay(), 0));
                bill.setDeductibleAmount(nvl(receipt.getSelfPay(), 0));
                bill.setPrepaidAmount(nvl(receipt.getPrepaidAmount(), 0));
                bill.setImmoderateAmount(nvl(receipt.getUnreasonableAmount(), 0));
                bill.setPartialDeductible(nvl(receipt.getClassResponsibility(), 0));
                BigDecimal reasonableAmt = bill.getBillAmount().subtract(bill.getDeductibleAmount())
                        .subtract(bill.getPrepaidAmount()).subtract(bill.getImmoderateAmount())
                        .subtract(bill.getPartialDeductible());
                if (reasonableAmt.compareTo(BigDecimal.ZERO) < 0) {
                    reasonableAmt = BigDecimal.ZERO;
                }
                bill.setReasonableAmount(reasonableAmt);
                bill.setBillNo(receipt.getReceiptNo());
                bill.setDays(wesureMedical.getInHospitalDays());
                String hospitailName = bill.getHospitalName();
                if (StringUtils.isEmpty(bill.getHospitalCode())
                        || "其他".equals(hospitailName)
                        || "其他医院".equals(hospitailName)) {
                    bill.setHospitalCode("9999999");
                }

                wesureMedicalToBillDetail(bill, receipt);
                wesureMedicalToBillDiagnose(bill, diseaseList);
                billList.add(bill);
            }
        }

        if (repeatNoSet.size() > 0) {
            throw new GlobalBusinessException("111111", "已有重复的发票号:" + repeatNoSet + "，是否继续新增账单数据？");
        }
        if (billList.size() == 0) {
            throw new GlobalBusinessException("微保账单为空");
        }
        return billList;
    }

    private void wesureMedicalToBillDetail(MedicalBillInfoVO billVO, WesureMedicalReceiptDTO receipt) {
        MedicalBillDetailDTO billDetailDTO = new MedicalBillDetailDTO();
        BeanUtils.copyProperties(billVO, billDetailDTO);
        billDetailDTO.setCostCode(receipt.getCostCode());
        List<MedicalBillDetailDTO> medicalBillDetailList = new ArrayList<>();
        medicalBillDetailList.add(billDetailDTO);
        billVO.setMedicalBillDetailDTOList(medicalBillDetailList);
    }

    private void wesureMedicalToBillDiagnose(MedicalBillInfoVO billVO, List<WesureMedicalDiseaseDTO> diseaseList) {
        List<DiagnoseHospitalBillAssociationDTO> diagnoseList = new ArrayList<>();
        for (int i = 0; i < diseaseList.size(); i++) {
            DiagnoseHospitalBillAssociationDTO diagnoseDTO = new DiagnoseHospitalBillAssociationDTO();
            diagnoseDTO.setDiagnoseCode(diseaseList.get(i).getDiseaseCode());
            diagnoseDTO.setDiagnoseName(diseaseList.get(i).getDiseaseName());
            diagnoseDTO.setDisplayNo(i);
            diagnoseDTO.setDiagnosticTypologyCode(BaseConstant.STRING_01);
            diagnoseDTO.setIsSurgical(ConfigConstValues.NO);
            diagnoseList.add(diagnoseDTO);
        }

        billVO.setDiagnoseHospitalBillAssociationList(diagnoseList);
    }

    /**
     * 查询保单历史报案已结案的发票金额信息
     *
     * @param reportNo
     * @return
     */
    @Override
    public BigDecimal getPolicyHistoryBillInfo(String reportNo) {
        String policyNo = ahcsPolicyInfoMapper.getOnePolicyNoByReportNo(reportNo);
        List<MedicalBillHistoryVo> medicalBillHistoryVoList = medicalBillInfoMapper.getPolicyHistoryBillInfo(policyNo);
        if (CollectionUtils.isEmpty(medicalBillHistoryVoList)) {
            return BigDecimal.ZERO;
        }
        //按报案号分组;
        Map<String, List<MedicalBillHistoryVo>> mapList = medicalBillHistoryVoList.stream().collect(Collectors.groupingBy(MedicalBillHistoryVo::getReportNo));
        LogUtil.info("报案号={}，保单号={}，历史发票信息={}", reportNo, policyNo, JsonUtils.toJsonString(mapList));
        BigDecimal sumReasonableAmount = BigDecimal.ZERO;
        for (Map.Entry<String, List<MedicalBillHistoryVo>> entry : mapList.entrySet()) {
            List<MedicalBillHistoryVo> medicalBillHistoryVos = entry.getValue();

            if (medicalBillHistoryVos.size() == 1) {
                MedicalBillHistoryVo vo = medicalBillHistoryVos.get(0);
                BigDecimal reasonableAmount = vo.getBillAmount().subtract(nvl(vo.getDeductibleAmount(), 0)).subtract(nvl(vo.getImmoderateAmount(), 0)).subtract(nvl(vo.getPrepaidAmount(), 0)).subtract(nvl(vo.getPartialDeductible(), 0));
                sumReasonableAmount = sumReasonableAmount.add(reasonableAmount);
            } else {
                //按赔付次数分组，取赔付次数最大的一个 降序排序 取赔付次数最大的 发票信息
                Map<Integer, List<MedicalBillHistoryVo>> mpaList = medicalBillHistoryVos.stream().sorted(Comparator.comparing(MedicalBillHistoryVo::getCaseTimes).reversed()).collect(Collectors.groupingBy(MedicalBillHistoryVo::getCaseTimes, LinkedHashMap::new, Collectors.toList()));
                for (Map.Entry<Integer, List<MedicalBillHistoryVo>> en : mpaList.entrySet()) {
                    List<MedicalBillHistoryVo> mlist = en.getValue();
                    for (MedicalBillHistoryVo vo : mlist) {
                        BigDecimal reasonableAmount = vo.getBillAmount().subtract(nvl(vo.getDeductibleAmount(), 0)).subtract(nvl(vo.getImmoderateAmount(), 0)).subtract(nvl(vo.getPrepaidAmount(), 0)).subtract(nvl(vo.getPartialDeductible(), 0));
                        sumReasonableAmount = sumReasonableAmount.add(reasonableAmount);
                    }
                    break;
                }
            }
        }

        return sumReasonableAmount;
    }

    /**
     * @param reportNo
     * @param caseTimes
     * @return
     */
    @Override
    public Map<String,CompareBillNoResultVO> checkAllBillDuplicate(String reportNo, int caseTimes) {
        List<MedicalBillInfoVO> medicalBillInfoVOS = medicalBillInfoMapper.getMedicalBillInfoForPrint(reportNo, caseTimes);
        if (CollectionUtils.isEmpty(medicalBillInfoVOS)) {
            return null;
        }
        Map<String,CompareBillNoResultVO> resultMap = new HashMap<>();
        for (MedicalBillInfoVO medicalBillInfoVO : medicalBillInfoVOS) {
            List<CompareBillNoResultVO> compareBillNoResultVOList =
                    medicalBillInfoMapper.getSameBillNoReportNo(medicalBillInfoVO);
            String tempReportNo = medicalBillInfoVO.getReportNo() + "_" + medicalBillInfoVO.getCaseTimes();
            // 本案是否存在重复校验。
            Optional<CompareBillNoResultVO> currentCaseBillOpt = compareBillNoResultVOList.stream().filter(item -> StringUtils.equals(item.getReportNo(), tempReportNo)
                    && !StringUtils.equals(medicalBillInfoVO.getIdAhcsBillInfo(), item.getIdAhcsBillInfo())).findFirst();
            // 其它案件是否存在重复校验
            List<CompareBillNoResultVO> otherCaseBillList =
                    compareBillNoResultVOList.stream().filter(item -> !StringUtils.equals(medicalBillInfoVO.getIdAhcsBillInfo(), item.getIdAhcsBillInfo())
                            && !StringUtils.startsWith(item.getReportNo(),reportNo)).distinct().collect(Collectors.toList());
            List<String> otherRepeatReportNoList = otherCaseBillList.stream().map(i -> i.getReportNo().substring(0,
                    i.getReportNo().indexOf("_"))).distinct().collect(Collectors.toList());
            otherRepeatReportNoList.remove(reportNo);
            if (!currentCaseBillOpt.isPresent() && CollectionUtils.isEmpty(otherRepeatReportNoList)) {
                continue;
            }

            CompareBillNoResultVO compareBillNoResult = new CompareBillNoResultVO();
            compareBillNoResult.setReportNo(reportNo);
            compareBillNoResult.setCaseTimes(caseTimes);
            compareBillNoResult.setBillNo(medicalBillInfoVO.getBillNo());
            compareBillNoResult.setIdAhcsBillInfo(medicalBillInfoVO.getIdAhcsBillInfo());
            currentCaseBillOpt.ifPresent(compareBillNoResult::setCurrentCaseSameBill);
            compareBillNoResult.setOtherCaseSameBillList(otherCaseBillList);
            compareBillNoResult.setOtherCaseReportNoList(otherRepeatReportNoList);
            resultMap.put(medicalBillInfoVO.getIdAhcsBillInfo(), compareBillNoResult);
        }

        return resultMap;
    }

    private void savePayMent(List<PaymentInfoVO> paymentList, String reportNo) {
            paymentList.forEach(p->{
                PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
                BeanUtils.copyProperties(p,paymentInfoDTO);
                paymentInfoDTO.setProvinceName(p.getProvinceCode());
                paymentInfoDTO.setReportNo(reportNo);
                // 数据库字段问题
                paymentInfoDTO.setProvinceName(p.getProvinceCode());
                String cityCode = p.getCityCode();
                if (BaseConstant.OTHER_CITY_CODE.equals(cityCode)){
                    paymentInfoDTO.setRegionCode(BaseConstant.OTHER_COUNTRY_CODE);
                } else {
                    List<CityDefineDTO> countys = cityDefineService.getCityDefineDTOList(cityCode, "city");
                    paymentInfoDTO.setRegionCode(Optional.ofNullable(countys).orElse(new ArrayList<>()).get(0).getCityCode());
                }
                if (StringUtils.isEmpty(p.getCollectPayApproach())){
                    paymentInfoDTO.setCollectPayApproach("02");
                }
                String bankDetail = p.getBankDetail();
                String bankDetailCode = checkBankIfExist(bankDetail);
                if (StringUtils.isEmpty(bankDetailCode)){
                    paymentInfoDTO.setBankDetail(null);
                } else {
                    paymentInfoDTO.setBankDetailCode(bankDetailCode);
                }
                paymentInfoDTO.setCaseTimes(Integer.valueOf(ReportConstant.INIT_CASE_TIMES));
//                if(com.paic.ncbs.claim.common.util.StringUtils.isNotEmpty(onlineReportVO.getCompanyId())){
                    paymentInfoDTO.setCreatedBy("system");
                    paymentInfoDTO.setUpdatedBy("system");
//                }
                paymentInfoDTO.setOpenId(p.getOpenId());
                paymentInfoDTO.setPayType(null == p.getPayType() ? "2":p.getPayType());
                paymentInfoDTO.setClientRelation(p.getClientRelation());
                if ("1".equals(paymentInfoDTO.getBankAccountAttribute())) {
                    Integer paymentInfoForOnly = paymentInfoMapper.getPaymentInfoForOnly(paymentInfoDTO);
                    if (paymentInfoForOnly>0) {
                        return;
                    }
                }
                paymentInfoService.addPaymentInfo(paymentInfoDTO);
            });

    }

    private String checkBankIfExist(String bankDetail) {
        return commonParameterMapper.getBankIfExistByBranchBankName(bankDetail);
    }

    @Override
    public void addOuterMedicalBillNoTaskInfo(MedicalDTO medicalDTO) throws Exception {
        //删除收单数据
        this.rollBackCheckDuty(medicalDTO);
        //1、根据报案号查询账单相关表是否录入数据，如果有则代表业务操作过则不再继续（完成）
        this.checkParameter(medicalDTO);
        //2、使用该接口的入参借鉴新增账单方法，将相关单证信息补充完整
        this.validAndAddOuterMedicalBill(medicalDTO);
    }
}