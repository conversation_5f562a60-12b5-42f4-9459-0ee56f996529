package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款理算配置-津贴类约束表
 */
@ApiModel("条款理算配置-津贴类约束表")
@Getter
@Setter
public class ClmsAllowanceConfig {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 就诊类型
     */
    @ApiModelProperty("就诊类型")
    private String visitType;

    /**
     * 是否这算有效住院天数
     */
    @ApiModelProperty("是否这算有效住院天数")
    private Boolean convertHosDays;

    /**
     * 前N天
     */
    @ApiModelProperty("前N天")
    private Integer beforeDays;

    /**
     * 后N天
     */
    @ApiModelProperty("后N天")
    private Integer afterDays;

    /**
     * 累计赔付天数
     */
    @ApiModelProperty("累计赔付天数")
    private Integer accPayDays;

    /**
     * 单次赔付天数
     */
    @ApiModelProperty("单次赔付天数")
    private Integer oncePayDays;

    /**
     * 次免赔天数
     */
    @ApiModelProperty("次免赔天数")
    private Integer onceNopayDays;

    /**
     * 津贴日额
     */
    @ApiModelProperty("津贴日额")
    private BigDecimal allowancePerDay;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
