package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款理赔配置表
 */
@ApiModel("条款理赔配置表")
@Getter
@Setter
public class ClmsClauseConfig {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 产品工厂的版本
     */
    @ApiModelProperty("产品工厂的版本")
    private String pfVersion;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Integer versionNo;

    /**
     * 来源
     */
    @ApiModelProperty("来源")
    private String defineSource;

    /**
     * 责任明细
     */
    @ApiModelProperty("责任明细")
    private String dutyDetail;

    /**
     * 个团标记
     */
    @ApiModelProperty("个团标记")
    private String igFlag;

    /**
     * 生效状态
     */
    @ApiModelProperty("生效状态")
    private String validFlag;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private Date effectTime;

    /**
     * 失效时间
     */
    @ApiModelProperty("失效时间")
    private Date expireTime;

    /**
     * 新增原因分类
     */
    @ApiModelProperty("新增原因分类")
    private String addReason;

    /**
     * 新增原因描述
     */
    @ApiModelProperty("新增原因描述")
    private String addReasonDesc;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
