package com.paic.ncbs.claim.service.autosettle;

import com.paic.ncbs.claim.model.dto.autosettle.ScenarioConfigDTO;
import com.paic.ncbs.claim.model.vo.autosettle.*;

import java.util.List;

/**
 * 场景配置服务接口
 */
public interface ScenarioConfigService {

    /**
     * 获取场景基础信息
     */
    ScenarioConfigVO getScenarioConfig(String productCode, String planCode, String riskGroupCode);

    /**
     * 保存场景基础信息
     */
    void saveScenarioConfig(ScenarioConfigVO scenarioConfigVO, String userUM);

    /**
     * 获取可选择的责任明细列表
     */
    List<DutyDetailSelectionVO> getSelectableDutyDetails(String productCode, String planCode, String riskGroupCode);

    /**
     * 获取医院范围配置列表
     */
    List<HospitalRangeConfigVO> getHospitalRangeConfigs(Integer configId);

    /**
     * 保存医院范围配置
     */
    void saveHospitalRangeConfig(HospitalRangeConfigVO hospitalRangeConfigVO, String userUM);

    /**
     * 删除医院范围配置
     */
    void deleteHospitalRangeConfig(Integer id, String userUM);

    /**
     * 获取疾病范围配置列表
     */
    List<DiseaseRangeConfigVO> getDiseaseRangeConfigs(Integer configId);

    /**
     * 保存疾病范围配置
     */
    void saveDiseaseRangeConfig(DiseaseRangeConfigVO diseaseRangeConfigVO, String userUM);

    /**
     * 删除疾病范围配置
     */
    void deleteDiseaseRangeConfig(Integer id, String userUM);

    /**
     * 获取三目录除外配置列表
     */
    List<Catalog3ExcludeConfigVO> getCatalog3ExcludeConfigs(Integer configId);

    /**
     * 保存三目录除外配置
     */
    void saveCatalog3ExcludeConfig(Catalog3ExcludeConfigVO catalog3ExcludeConfigVO, String userUM);

    /**
     * 删除三目录除外配置
     */
    void deleteCatalog3ExcludeConfig(Integer id, String userUM);

    /**
     * 获取其他核责配置列表
     */
    List<OtherVerifyConfigVO> getOtherVerifyConfigs(Integer configId);

    /**
     * 保存其他核责配置
     */
    void saveOtherVerifyConfig(OtherVerifyConfigVO otherVerifyConfigVO, String userUM);

    /**
     * 删除其他核责配置
     */
    void deleteOtherVerifyConfig(Integer id, String userUM);

    /**
     * 获取等待期配置列表
     */
    List<WaitPeriodConfigVO> getWaitPeriodConfigs(Integer configId);

    /**
     * 保存等待期配置
     */
    void saveWaitPeriodConfig(WaitPeriodConfigVO waitPeriodConfigVO, String userUM);

    /**
     * 获取责任理算顺序配置列表
     */
    List<DutyCalcOrderConfigVO> getDutyCalcOrderConfigs(Integer configId);

    /**
     * 保存责任理算顺序配置
     */
    void saveDutyCalcOrderConfig(List<DutyCalcOrderConfigVO> dutyCalcOrderConfigVOs, String userUM);

    /**
     * 获取可用的核责规则列表
     */
    List<VerifyRuleVO> getAvailableVerifyRules();

    /**
     * 搜索医院
     */
    List<HospitalVO> searchHospitals(String hospitalName, String orgType);

    /**
     * 搜索疾病ICD
     */
    List<DiseaseIcdVO> searchDiseaseIcds(String searchStr, String diagnosisCategoryCode);

    /**
     * 搜索药品
     */
    List<MedicineVO> searchMedicines(String medicineName, Integer packageId);

    /**
     * 获取省市区列表
     */
    List<ExcludeRegionVO> getRegionList();
}
