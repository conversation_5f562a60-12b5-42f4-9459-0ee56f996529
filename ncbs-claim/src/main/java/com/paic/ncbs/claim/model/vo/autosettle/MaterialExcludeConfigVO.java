package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 除外器材配置VO（暂不考虑）
 */
@ApiModel("除外器材配置VO")
@Getter
@Setter
public class MaterialExcludeConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 器材代码
     */
    @ApiModelProperty("器材代码")
    private String materialCode;

    /**
     * 器材名称
     */
    @ApiModelProperty("器材名称")
    private String materialName;
}
