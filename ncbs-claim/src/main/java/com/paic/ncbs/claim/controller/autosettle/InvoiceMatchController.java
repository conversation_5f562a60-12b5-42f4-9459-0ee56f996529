package com.paic.ncbs.claim.controller.autosettle;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.match.AutoMatchRequestDTO;
import com.paic.ncbs.claim.model.dto.match.AutoMatchResultDTO;
import com.paic.ncbs.claim.model.dto.match.InvoiceMatchDTO;
import com.paic.ncbs.claim.service.match.InvoiceMatchService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发票匹责控制器
 */
@RestController
@RequestMapping("/api/match")
public class InvoiceMatchController {
    
    @Autowired
    private InvoiceMatchService invoiceMatchService;
    
    /**
     * 保存人工匹责结果
     */
    @PostMapping("/manual/save")
    public String saveManualMatch(@RequestBody List<InvoiceMatchDTO> matchList,
                                  @RequestParam String userUM) {
        try {
            if (StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("操作用户不能为空");
            }
            
            invoiceMatchService.saveManualMatch(matchList, userUM);
            return "保存成功";
        } catch (Exception e) {
            LogUtil.audit("保存人工匹责结果失败：{}", e.getMessage());
            throw new GlobalBusinessException("保存失败：" + e.getMessage());
        }
    }
    
    /**
     * 自动匹责
     */
    @PostMapping("/auto")
    public AutoMatchResultDTO autoMatch(@RequestBody AutoMatchRequestDTO request) {
        try {
            return invoiceMatchService.autoMatch(request);
        } catch (Exception e) {
            LogUtil.audit("自动匹责失败：{}", e.getMessage());
            throw new GlobalBusinessException("自动匹责失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询匹责结果
     */
    @GetMapping("/results")
    public List<InvoiceMatchDTO> getMatchResults(@RequestParam String reportNo,
                                                 @RequestParam Integer caseTimes) {
        try {
            if (StringUtils.isBlank(reportNo) || caseTimes == null) {
                throw new GlobalBusinessException("报案号和赔付次数不能为空");
            }
            
            return invoiceMatchService.getMatchResults(reportNo, caseTimes);
        } catch (Exception e) {
            LogUtil.audit("查询匹责结果失败：{}", e.getMessage());
            throw new GlobalBusinessException("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据发票ID查询匹责结果
     */
    @GetMapping("/results/bill/{billId}")
    public List<InvoiceMatchDTO> getMatchResultsByBillId(@PathVariable String billId) {
        try {
            if (StringUtils.isBlank(billId)) {
                throw new GlobalBusinessException("发票ID不能为空");
            }
            
            return invoiceMatchService.getMatchResultsByBillId(billId);
        } catch (Exception e) {
            LogUtil.audit("根据发票ID查询匹责结果失败：{}", e.getMessage());
            throw new GlobalBusinessException("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除匹责结果
     */
    @DeleteMapping("/results")
    public String deleteMatchResults(@RequestParam String reportNo,
                                     @RequestParam Integer caseTimes,
                                     @RequestParam String userUM) {
        try {
            if (StringUtils.isBlank(reportNo) || caseTimes == null || StringUtils.isBlank(userUM)) {
                throw new GlobalBusinessException("报案号、赔付次数和操作用户不能为空");
            }
            
            invoiceMatchService.deleteMatchResults(reportNo, caseTimes, userUM);
            return "删除成功";
        } catch (Exception e) {
            LogUtil.audit("删除匹责结果失败：{}", e.getMessage());
            throw new GlobalBusinessException("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查是否需要自动匹责
     */
    @GetMapping("/check/need-auto")
    public boolean needAutoMatch(@RequestParam String reportNo,
                                 @RequestParam Integer caseTimes) {
        try {
            if (StringUtils.isBlank(reportNo) || caseTimes == null) {
                throw new GlobalBusinessException("报案号、赔付次数和产品代码不能为空");
            }
            
            return invoiceMatchService.needAutoMatch(reportNo, caseTimes);
        } catch (Exception e) {
            LogUtil.audit("检查是否需要自动匹责失败：{}", e.getMessage());
            throw new GlobalBusinessException("检查失败：" + e.getMessage());
        }
    }
    
    /**
     * 验证人工匹责结果
     */
    @PostMapping("/validate")
    public String validateManualMatch(@RequestParam String reportNo,
                                      @RequestParam Integer caseTimes,
                                      @RequestParam String productCode) {
        try {
            if (StringUtils.isBlank(reportNo) || caseTimes == null || StringUtils.isBlank(productCode)) {
                throw new GlobalBusinessException("报案号、赔付次数和产品代码不能为空");
            }
            
            invoiceMatchService.validateManualMatch(reportNo, caseTimes, productCode);
            return "验证通过";
        } catch (Exception e) {
            LogUtil.audit("验证人工匹责结果失败：{}", e.getMessage());
            throw new GlobalBusinessException("验证失败：" + e.getMessage());
        }
    }
}
