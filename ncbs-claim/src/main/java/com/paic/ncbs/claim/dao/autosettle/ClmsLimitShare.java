package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款理算配置-费用共享约束表
 */
@ApiModel("条款理算配置-费用共享约束表")
@Getter
@Setter
public class ClmsLimitShare {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 约束表id
     */
    @ApiModelProperty("约束表id")
    private Integer feeConfigId;

    /**
     * 共享类型
     */
    @ApiModelProperty("共享类型")
    private String shareType;

    /**
     * 月赔付次数定义-用于月赔付次数
     */
    @ApiModelProperty("月赔付次数定义-用于月赔付次数")
    private String timesMonthDefine;

    /**
     * 共享额度/次数
     */
    @ApiModelProperty("共享额度/次数")
    private BigDecimal shareValue;

    /**
     * 次定义
     */
    @ApiModelProperty("次定义")
    private String timesDefine;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
