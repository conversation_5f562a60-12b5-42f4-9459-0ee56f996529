package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 场景配置基础信息VO
 */
@ApiModel("场景配置基础信息VO")
@Getter
@Setter
public class ScenarioConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer configId;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 选中的责任明细列表
     */
    @ApiModelProperty("选中的责任明细列表")
    private List<DutyDetailSelectionVO> selectedDutyDetails;

    /**
     * 配置状态
     */
    @ApiModelProperty("配置状态")
    private String configStatus;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String createTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private String updateTime;
}
