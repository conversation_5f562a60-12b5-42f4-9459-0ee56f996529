package com.paic.ncbs.claim.model.dto.autosettle;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 医院范围配置DTO
 */
@Getter
@Setter
public class HospitalRangeConfigDTO {

    /**
     * 配置ID
     */
    private Integer id;

    /**
     * 外键配置ID
     */
    private Integer configId;

    /**
     * 核责等级
     */
    private Byte verifyLevel;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 险种代码
     */
    private String planCode;

    /**
     * 险种名称
     */
    private String planName;

    /**
     * 方案代码
     */
    private String riskGroupCode;

    /**
     * 方案名称
     */
    private String riskGroupName;

    /**
     * 责任代码
     */
    private String dutyCode;

    /**
     * 责任名称
     */
    private String dutyName;

    /**
     * 责任明细代码
     */
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    private String dutyDetailName;

    /**
     * 医院等级
     */
    private String hospitalLevel;

    /**
     * 医院级别
     */
    private String hospitalGrade;

    /**
     * 医院性质
     */
    private String hospitalNature;

    /**
     * 就诊分类
     */
    private String medicalConsultation;

    /**
     * 医院区域
     */
    private String hospitalRegion;

    /**
     * 除外区域
     */
    private String exceptRegion;

    /**
     * 包含医院包ID
     */
    private Integer includePackageId;

    /**
     * 排除医院包ID
     */
    private Integer excludePackageId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date sysCtime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date sysUtime;
}
