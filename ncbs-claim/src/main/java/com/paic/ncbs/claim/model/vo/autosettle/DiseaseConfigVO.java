package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 疾病配置VO
 */
@ApiModel("疾病配置VO")
@Getter
@Setter
public class DiseaseConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 诊断分类代码列表
     */
    @ApiModelProperty("诊断分类代码列表")
    private List<String> diagnosisCategoryCodes;

    /**
     * 诊断分类名称列表
     */
    @ApiModelProperty("诊断分类名称列表")
    private List<String> diagnosisCategoryNames;

    /**
     * 疾病ICD列表
     */
    @ApiModelProperty("疾病ICD列表")
    private List<DiseaseIcdVO> diseaseIcds;

    /**
     * 排除的疾病ICD列表
     */
    @ApiModelProperty("排除的疾病ICD列表")
    private List<DiseaseIcdVO> excludeIcds;
}
