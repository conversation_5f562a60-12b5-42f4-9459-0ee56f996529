package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 除外诊疗配置VO（暂不考虑）
 */
@ApiModel("除外诊疗配置VO")
@Getter
@Setter
public class TreatmentExcludeConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 诊疗项目代码
     */
    @ApiModelProperty("诊疗项目代码")
    private String treatmentCode;

    /**
     * 诊疗项目名称
     */
    @ApiModelProperty("诊疗项目名称")
    private String treatmentName;
}
