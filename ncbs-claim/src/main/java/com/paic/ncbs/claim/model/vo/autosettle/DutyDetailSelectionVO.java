package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 责任明细选择VO
 */
@ApiModel("责任明细选择VO")
@Getter
@Setter
public class DutyDetailSelectionVO {

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 责任明细分类
     */
    @ApiModelProperty("责任明细分类")
    private String dutyDetailType;

    /**
     * 是否选中
     */
    @ApiModelProperty("是否选中")
    private Boolean selected;

    /**
     * 是否可选择（根据责任明细分类判断）
     */
    @ApiModelProperty("是否可选择")
    private Boolean selectable;
}
