package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票匹责结果表
 */
@ApiModel("发票匹责结果表")
@Getter
@Setter
public class ClmsInvoiceMatch {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 案件号
     */
    @ApiModelProperty("案件号")
    private String reportNo;

    /**
     * 赔付次数
     */
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;

    /**
     * 匹责类型
     */
    @ApiModelProperty("匹责类型")
    private String matchType;

    /**
     * 自动匹责的配置项id-自动匹责时填充
     */
    @ApiModelProperty("自动匹责的配置项id-自动匹责时填充")
    private Integer configId;

    /**
     * 发票账单信息表主键
     */
    @ApiModelProperty("发票账单信息表主键")
    private String idAhcsInvoiceInfo;

    /**
     * 执行成功标志
     */
    @ApiModelProperty("执行成功标志")
    private Byte matchSign;

    /**
     * 失败描述
     */
    @ApiModelProperty("失败描述")
    private String failDesc;

    /**
     * 匹责结论
     */
    @ApiModelProperty("匹责结论")
    private String matchResult;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
