package com.paic.ncbs.claim.service.verify.impl;

import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.mapper.autosettle.*;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.verify.AutoVerifyRequestDTO;
import com.paic.ncbs.claim.model.dto.verify.AutoVerifyResultDTO;
import com.paic.ncbs.claim.service.verify.AutoVerifyService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 自动核责服务实现类
 */
@Service
public class AutoVerifyServiceImpl implements AutoVerifyService {
    
    @Autowired
    private ClmsVerifyLogMapper verifyLogMapper;

    @Autowired
    private ClmsVerifySublogMapper verifySublogMapper;

    @Autowired
    private ClmsClauseConfigMapper clauseConfigMapper;
    
    @Override
    @Transactional
    public AutoVerifyResultDTO autoVerify(AutoVerifyRequestDTO request) throws GlobalBusinessException {
        LogUtil.audit("开始自动核责，报案号：{}，赔付次数：{}", request.getReportNo(), request.getCaseTimes());
        
        AutoVerifyResultDTO result = new AutoVerifyResultDTO();
        result.setReportNo(request.getReportNo());
        result.setCaseTimes(request.getCaseTimes());
        result.setVerifyResults(new ArrayList<>());
        
        try {
            // 1. 检查是否需要自动核责
            if (!needAutoVerify(request.getReportNo(), request.getCaseTimes(), request.getProductCode())) {
                result.setSuccess(false);
                result.setFailReason("不需要自动核责");
                return result;
            }
            
            // 2. 检查匹责结果
            if (CollectionUtils.isEmpty(request.getMatchResults())) {
                result.setSuccess(false);
                result.setFailReason("无匹责结果，无法进行核责");
                LogUtil.audit("无匹责结果，无法进行核责");
                return result;
            }
            
            // 3. 查找有效的理赔配置
            Integer configId = clauseConfigMapper.getEffectiveConfigId(request.getProductCode(), request.getPlanCode());
            if (configId == null) {
                result.setSuccess(false);
                result.setFailReason("未找到有效的理赔配置项");
                LogUtil.audit("未找到有效的理赔配置项，产品代码：{}，方案代码：{}", request.getProductCode(), request.getPlanCode());
                return result;
            }
            
            // 4. 执行自动核责逻辑（简化实现）
            LogUtil.audit("开始执行自动核责逻辑");

            // 5. 保存核责结果（简化实现）
            LogUtil.audit("保存核责结果");
            
            result.setSuccess(true);
            LogUtil.audit("自动核责成功，报案号：{}，赔付次数：{}", request.getReportNo(), request.getCaseTimes());
            
        } catch (Exception e) {
            result.setSuccess(false);
            result.setFailReason("自动核责异常：" + e.getMessage());
            LogUtil.audit("自动核责异常：{}", e.getMessage());
            throw new GlobalBusinessException("自动核责失败：" + e.getMessage());
        }
        
        return result;
    }
    


    
    @Override
    public boolean needAutoVerify(String reportNo, Integer caseTimes, String productCode) {
        // 1. 检查产品线是否为意外险或健康险
        if (!isHealthOrAccidentProduct(productCode)) {
            LogUtil.audit("产品线非意外险/健康险，不需要自动核责，产品代码：{}", productCode);
            return false;
        }
        
        return true;
    }
    
    @Override
    public AutoVerifyResultDTO getVerifyResults(String reportNo, Integer caseTimes) {
        // 简化实现，返回基本结果
        AutoVerifyResultDTO result = new AutoVerifyResultDTO();
        result.setReportNo(reportNo);
        result.setCaseTimes(caseTimes);
        result.setSuccess(true);
        result.setVerifyResults(new ArrayList<>());

        LogUtil.audit("查询核责结果，报案号：{}，赔付次数：{}", reportNo, caseTimes);
        return result;
    }

    // 辅助方法
    private boolean isHealthOrAccidentProduct(String productCode) {
        return StringUtils.isNotBlank(productCode) &&
               (productCode.startsWith("H") || productCode.startsWith("A"));
    }
}
