package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 责任理算顺序配置VO
 */
@ApiModel("责任理算顺序配置VO")
@Getter
@Setter
public class DutyCalcOrderConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 产品代码
     */
    @ApiModelProperty("产品代码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 险种代码
     */
    @ApiModelProperty("险种代码")
    private String planCode;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 方案代码
     */
    @ApiModelProperty("方案代码")
    private String riskGroupCode;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 责任代码
     */
    @ApiModelProperty("责任代码")
    private String dutyCode;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细代码
     */
    @ApiModelProperty("责任明细代码")
    private String dutyDetailCode;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 责任明细分类
     */
    @ApiModelProperty("责任明细分类")
    private String dutyDetailType;

    /**
     * 理算顺序
     */
    @ApiModelProperty("理算顺序")
    private Integer calcOrder;

    /**
     * 关联责任代码
     */
    @ApiModelProperty("关联责任代码")
    private String relatedDutyCode;

    /**
     * 关联责任名称
     */
    @ApiModelProperty("关联责任名称")
    private String relatedDutyName;

    /**
     * 关联责任明细代码
     */
    @ApiModelProperty("关联责任明细代码")
    private String relatedDutyDetailCode;

    /**
     * 关联责任明细名称
     */
    @ApiModelProperty("关联责任明细名称")
    private String relatedDutyDetailName;

    /**
     * 是否同步理算
     */
    @ApiModelProperty("是否同步理算")
    private Boolean syncCalc;
}
