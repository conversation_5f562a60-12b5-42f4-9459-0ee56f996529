package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 核责规则VO
 */
@ApiModel("核责规则VO")
@Getter
@Setter
public class VerifyRuleVO {

    /**
     * 规则代码
     */
    @ApiModelProperty("规则代码")
    private String ruleCode;

    /**
     * 规则类别
     */
    @ApiModelProperty("规则类别")
    private String ruleCategory;

    /**
     * 规则名称
     */
    @ApiModelProperty("规则名称")
    private String ruleName;

    /**
     * 规则层级
     */
    @ApiModelProperty("规则层级")
    private String ruleLevel;

    /**
     * 规则逻辑描述
     */
    @ApiModelProperty("规则逻辑描述")
    private String ruleLogic;

    /**
     * 是否选中
     */
    @ApiModelProperty("是否选中")
    private Boolean selected;

    /**
     * 实现类
     */
    @ApiModelProperty("实现类")
    private String implClass;
}
