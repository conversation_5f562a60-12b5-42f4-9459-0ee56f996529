package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 疾病范围配置VO
 */
@ApiModel("疾病范围配置VO")
@Getter
@Setter
public class DiseaseRangeConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 核责事项
     */
    @ApiModelProperty("核责事项")
    private String verifyItem = "疾病范围";

    /**
     * 赔付疾病列表
     */
    @ApiModelProperty("赔付疾病列表")
    private List<DiseaseConfigVO> payableDiseases;

    /**
     * 除外疾病列表
     */
    @ApiModelProperty("除外疾病列表")
    private List<DiseaseConfigVO> excludeDiseases;
}
