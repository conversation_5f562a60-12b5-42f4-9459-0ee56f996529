package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 其他核责配置VO
 */
@ApiModel("其他核责配置VO")
@Getter
@Setter
public class OtherVerifyConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 核责事项
     */
    @ApiModelProperty("核责事项")
    private String verifyItem = "其他核责";

    /**
     * 选中的核责规则列表
     */
    @ApiModelProperty("选中的核责规则列表")
    private List<VerifyRuleVO> selectedRules;
}
