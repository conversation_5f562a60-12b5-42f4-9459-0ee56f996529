package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 医疗目录表
 */
@ApiModel("医疗目录表")
@Getter
@Setter
public class ClmsMedicalDir {

    /**
     * 目录id
     */
    @ApiModelProperty("目录id")
    private Integer id;

    /**
     * 目录定义名称
     */
    @ApiModelProperty("目录定义名称")
    private String dirName;

    /**
     * 上级目录id
     */
    @ApiModelProperty("上级目录id")
    private Integer parentId;

    /**
     * 目录层级
     */
    @ApiModelProperty("目录层级")
    private Byte dirLevel;

    /**
     * 有效标志
     */
    @ApiModelProperty("有效标志")
    private Boolean validFlag;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
