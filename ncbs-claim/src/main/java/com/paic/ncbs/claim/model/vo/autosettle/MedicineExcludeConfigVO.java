package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 除外药品配置VO
 */
@ApiModel("除外药品配置VO")
@Getter
@Setter
public class MedicineExcludeConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 药品模板ID
     */
    @ApiModelProperty("药品模板ID")
    private Integer medicineTemplateId;

    /**
     * 药品模板名称
     */
    @ApiModelProperty("药品模板名称")
    private String medicineTemplateName;

    /**
     * 药品模板分类（分子药品模板、产品药品模板）
     */
    @ApiModelProperty("药品模板分类")
    private String medicineTemplateType;

    /**
     * 除外药品列表
     */
    @ApiModelProperty("除外药品列表")
    private List<MedicineVO> excludeMedicines;

    /**
     * 排除的药品列表（不在除外范围内的药品）
     */
    @ApiModelProperty("排除的药品列表")
    private List<MedicineVO> excludeFromExcludeMedicines;
}
