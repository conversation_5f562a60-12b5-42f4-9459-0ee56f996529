package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * 条款核责配置-三目录除外药品包表
 */
@ApiModel("条款核责配置-三目录除外药品包表")
@Getter
@Setter
public class ClmsVerifyCatalog3Pack {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 核责三目录id
     */
    @ApiModelProperty("核责三目录id")
    private Integer catalog3Id;

    /**
     * 药品包ID
     */
    @ApiModelProperty("药品包ID")
    private Integer packageId;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
