package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 疾病ICD VO
 */
@ApiModel("疾病ICD VO")
@Getter
@Setter
public class DiseaseIcdVO {

    /**
     * ICD代码
     */
    @ApiModelProperty("ICD代码")
    private String icdCode;

    /**
     * ICD名称
     */
    @ApiModelProperty("ICD名称")
    private String icdName;

    /**
     * 诊断分类代码
     */
    @ApiModelProperty("诊断分类代码")
    private String diagnosisCategoryCode;

    /**
     * 诊断分类名称
     */
    @ApiModelProperty("诊断分类名称")
    private String diagnosisCategoryName;

    /**
     * 是否排除
     */
    @ApiModelProperty("是否排除")
    private Boolean excluded;
}
