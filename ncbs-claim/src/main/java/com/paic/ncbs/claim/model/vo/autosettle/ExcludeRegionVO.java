package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 除外省市区域VO
 */
@ApiModel("除外省市区域VO")
@Getter
@Setter
public class ExcludeRegionVO {

    /**
     * 省代码
     */
    @ApiModelProperty("省代码")
    private String provinceCode;

    /**
     * 省名称
     */
    @ApiModelProperty("省名称")
    private String provinceName;

    /**
     * 市代码
     */
    @ApiModelProperty("市代码")
    private String cityCode;

    /**
     * 市名称
     */
    @ApiModelProperty("市名称")
    private String cityName;

    /**
     * 区代码
     */
    @ApiModelProperty("区代码")
    private String regionCode;

    /**
     * 区名称
     */
    @ApiModelProperty("区名称")
    private String regionName;
}
