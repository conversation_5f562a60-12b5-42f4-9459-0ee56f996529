package com.paic.ncbs.claim.model.dto.autosettle;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 场景配置DTO
 */
@Getter
@Setter
public class ScenarioConfigDTO {

    /**
     * 配置ID
     */
    private Integer configId;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 险种代码
     */
    private String planCode;

    /**
     * 险种名称
     */
    private String planName;

    /**
     * 方案代码
     */
    private String riskGroupCode;

    /**
     * 方案名称
     */
    private String riskGroupName;

    /**
     * 选中的责任明细列表
     */
    private List<DutyDetailSelectionDTO> selectedDutyDetails;

    /**
     * 配置状态
     */
    private String configStatus;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updateTime;
}
