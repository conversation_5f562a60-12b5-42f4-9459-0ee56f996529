package com.paic.ncbs.claim.service.autosettle.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.dao.autosettle.*;
import com.paic.ncbs.claim.dao.mapper.autosettle.*;
import com.paic.ncbs.claim.dao.mapper.checkloss.DiagnoseDefineMapper;
import com.paic.ncbs.claim.dao.mapper.checkloss.HospitalInfoMapper;
import com.paic.ncbs.claim.dao.mapper.other.BaseDataMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.autosettle.ScenarioConfigDTO;
import com.paic.ncbs.claim.model.vo.autosettle.*;
import com.paic.ncbs.claim.service.autosettle.ScenarioConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 场景配置服务实现类
 */
@Service
public class ScenarioConfigServiceImpl implements ScenarioConfigService {

    @Autowired
    private ClmsClauseConfigMapper clmsClauseConfigMapper;

    @Autowired
    private ClmsVerifyHospitalMapper clmsVerifyHospitalMapper;

    @Autowired
    private ClmsVerifyDiseaseMapper clmsVerifyDiseaseMapper;

    @Autowired
    private ClmsVerifyCatalog3Mapper clmsVerifyCatalog3Mapper;

    @Autowired
    private ClmsVerifyOtherMapper clmsVerifyOtherMapper;

    @Autowired
    private ClmsVerifyWaitPeriodMapper clmsVerifyWaitPeriodMapper;

    @Autowired
    private ClmsCalcSeqMapper clmsCalcSeqMapper;

    @Autowired
    private ClmsVerifyOtherItemMapper clmsVerifyOtherItemMapper;

    @Autowired
    private HospitalInfoMapper hospitalInfoMapper;

    @Autowired
    private DiagnoseDefineMapper diagnoseDefineMapper;

    @Autowired
    private BaseDataMapper baseDataMapper;

    @Override
    public ScenarioConfigVO getScenarioConfig(String productCode, String planCode, String riskGroupCode) {
        if (StringUtils.isBlank(productCode) || StringUtils.isBlank(planCode) || StringUtils.isBlank(riskGroupCode)) {
            throw new GlobalBusinessException("产品代码、险种代码和方案代码不能为空");
        }

        ScenarioConfigVO scenarioConfigVO = new ScenarioConfigVO();
        scenarioConfigVO.setProductCode(productCode);
        scenarioConfigVO.setPlanCode(planCode);
        scenarioConfigVO.setRiskGroupCode(riskGroupCode);

        // 查询已配置的责任明细
        List<DutyDetailSelectionVO> selectedDutyDetails = getSelectableDutyDetails(productCode, planCode, riskGroupCode);
        scenarioConfigVO.setSelectedDutyDetails(selectedDutyDetails);

        return scenarioConfigVO;
    }

    @Override
    @Transactional
    public void saveScenarioConfig(ScenarioConfigVO scenarioConfigVO, String userUM) {
        if (scenarioConfigVO == null) {
            throw new GlobalBusinessException("场景配置信息不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        // 保存场景配置基础信息
        ClmsClauseConfig clmsClauseConfig = new ClmsClauseConfig();
        BeanUtils.copyProperties(scenarioConfigVO, clmsClauseConfig);
        clmsClauseConfig.setCreatedBy(userUM);
        clmsClauseConfig.setSysCtime(new Date());
        clmsClauseConfig.setUpdatedBy(userUM);
        clmsClauseConfig.setSysUtime(new Date());

        if (scenarioConfigVO.getConfigId() == null) {
            clmsClauseConfigMapper.insertSelective(clmsClauseConfig);
        } else {
            clmsClauseConfig.setId(scenarioConfigVO.getConfigId());
            clmsClauseConfigMapper.updateByPrimaryKeySelective(clmsClauseConfig);
        }

        LogUtil.audit("保存场景配置成功，配置ID：{}", clmsClauseConfig.getId());
    }

    @Override
    public List<DutyDetailSelectionVO> getSelectableDutyDetails(String productCode, String planCode, String riskGroupCode) {
        // TODO: 实现获取可选择的责任明细列表
        // 这里需要根据产品工厂获取责任明细信息，并判断责任明细分类是否为"医疗费用"或"津贴"
        List<DutyDetailSelectionVO> dutyDetailSelectionVOs = new ArrayList<>();
        
        // 示例数据，实际需要从产品工厂获取
        DutyDetailSelectionVO dutyDetail1 = new DutyDetailSelectionVO();
        dutyDetail1.setDutyCode("DUTY001");
        dutyDetail1.setDutyName("住院医疗责任");
        dutyDetail1.setDutyDetailCode("DETAIL001");
        dutyDetail1.setDutyDetailName("住院医疗费用");
        dutyDetail1.setDutyDetailType("医疗费用");
        dutyDetail1.setSelectable(true);
        dutyDetail1.setSelected(false);
        dutyDetailSelectionVOs.add(dutyDetail1);

        return dutyDetailSelectionVOs;
    }

    @Override
    public List<HospitalRangeConfigVO> getHospitalRangeConfigs(Integer configId) {
        if (configId == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        // TODO: 实现获取医院范围配置列表
        List<HospitalRangeConfigVO> hospitalRangeConfigVOs = new ArrayList<>();
        return hospitalRangeConfigVOs;
    }

    @Override
    @Transactional
    public void saveHospitalRangeConfig(HospitalRangeConfigVO hospitalRangeConfigVO, String userUM) {
        if (hospitalRangeConfigVO == null) {
            throw new GlobalBusinessException("医院范围配置信息不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        // 校验必填字段
        if (StringUtils.isBlank(hospitalRangeConfigVO.getRiskGroupName())) {
            throw new GlobalBusinessException("方案名称不能为空");
        }

        // 校验是否重复配置
        // TODO: 实现重复配置校验逻辑

        // 保存医院范围配置
        ClmsVerifyHospital clmsVerifyHospital = new ClmsVerifyHospital();
        // TODO: 实现属性映射和保存逻辑

        LogUtil.audit("保存医院范围配置成功，配置ID：{}", clmsVerifyHospital.getId());
    }

    @Override
    @Transactional
    public void deleteHospitalRangeConfig(Integer id, String userUM) {
        if (id == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        clmsVerifyHospitalMapper.deleteByPrimaryKey(id);
        LogUtil.audit("删除医院范围配置成功，配置ID：{}", id);
    }

    @Override
    public List<DiseaseRangeConfigVO> getDiseaseRangeConfigs(Integer configId) {
        if (configId == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        // TODO: 实现获取疾病范围配置列表
        List<DiseaseRangeConfigVO> diseaseRangeConfigVOs = new ArrayList<>();
        return diseaseRangeConfigVOs;
    }

    @Override
    @Transactional
    public void saveDiseaseRangeConfig(DiseaseRangeConfigVO diseaseRangeConfigVO, String userUM) {
        if (diseaseRangeConfigVO == null) {
            throw new GlobalBusinessException("疾病范围配置信息不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        // 校验必填字段
        if (StringUtils.isBlank(diseaseRangeConfigVO.getRiskGroupName())) {
            throw new GlobalBusinessException("方案名称不能为空");
        }

        // 校验疾病数据
        if (CollectionUtils.isEmpty(diseaseRangeConfigVO.getPayableDiseases()) &&
            CollectionUtils.isEmpty(diseaseRangeConfigVO.getExcludeDiseases())) {
            throw new GlobalBusinessException("必须配置赔付疾病或除外疾病");
        }

        // TODO: 实现疾病范围配置保存逻辑

        LogUtil.audit("保存疾病范围配置成功");
    }

    @Override
    @Transactional
    public void deleteDiseaseRangeConfig(Integer id, String userUM) {
        if (id == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        clmsVerifyDiseaseMapper.deleteByPrimaryKey(id);
        LogUtil.audit("删除疾病范围配置成功，配置ID：{}", id);
    }

    @Override
    public List<Catalog3ExcludeConfigVO> getCatalog3ExcludeConfigs(Integer configId) {
        if (configId == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        // TODO: 实现获取三目录除外配置列表
        List<Catalog3ExcludeConfigVO> catalog3ExcludeConfigVOs = new ArrayList<>();
        return catalog3ExcludeConfigVOs;
    }

    @Override
    @Transactional
    public void saveCatalog3ExcludeConfig(Catalog3ExcludeConfigVO catalog3ExcludeConfigVO, String userUM) {
        if (catalog3ExcludeConfigVO == null) {
            throw new GlobalBusinessException("三目录除外配置信息不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        // 校验必填字段
        if (StringUtils.isBlank(catalog3ExcludeConfigVO.getRiskGroupName())) {
            throw new GlobalBusinessException("方案名称不能为空");
        }

        // 校验三目录除外数据
        if (CollectionUtils.isEmpty(catalog3ExcludeConfigVO.getExcludeMedicines())) {
            throw new GlobalBusinessException("必须配置除外药品");
        }

        // TODO: 实现三目录除外配置保存逻辑

        LogUtil.audit("保存三目录除外配置成功");
    }

    @Override
    @Transactional
    public void deleteCatalog3ExcludeConfig(Integer id, String userUM) {
        if (id == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        clmsVerifyCatalog3Mapper.deleteByPrimaryKey(id);
        LogUtil.audit("删除三目录除外配置成功，配置ID：{}", id);
    }

    @Override
    public List<OtherVerifyConfigVO> getOtherVerifyConfigs(Integer configId) {
        if (configId == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        // TODO: 实现获取其他核责配置列表
        List<OtherVerifyConfigVO> otherVerifyConfigVOs = new ArrayList<>();
        return otherVerifyConfigVOs;
    }

    @Override
    @Transactional
    public void saveOtherVerifyConfig(OtherVerifyConfigVO otherVerifyConfigVO, String userUM) {
        if (otherVerifyConfigVO == null) {
            throw new GlobalBusinessException("其他核责配置信息不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        // 校验必填字段
        if (StringUtils.isBlank(otherVerifyConfigVO.getRiskGroupName())) {
            throw new GlobalBusinessException("方案名称不能为空");
        }

        // 校验核责规则
        if (CollectionUtils.isEmpty(otherVerifyConfigVO.getSelectedRules())) {
            throw new GlobalBusinessException("必须选择核责规则");
        }

        // TODO: 实现其他核责配置保存逻辑

        LogUtil.audit("保存其他核责配置成功");
    }

    @Override
    @Transactional
    public void deleteOtherVerifyConfig(Integer id, String userUM) {
        if (id == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        clmsVerifyOtherMapper.deleteByPrimaryKey(id);
        LogUtil.audit("删除其他核责配置成功，配置ID：{}", id);
    }

    @Override
    public List<WaitPeriodConfigVO> getWaitPeriodConfigs(Integer configId) {
        if (configId == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        // TODO: 实现获取等待期配置列表
        List<WaitPeriodConfigVO> waitPeriodConfigVOs = new ArrayList<>();
        return waitPeriodConfigVOs;
    }

    @Override
    @Transactional
    public void saveWaitPeriodConfig(WaitPeriodConfigVO waitPeriodConfigVO, String userUM) {
        if (waitPeriodConfigVO == null) {
            throw new GlobalBusinessException("等待期配置信息不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        // TODO: 实现等待期配置保存逻辑

        LogUtil.audit("保存等待期配置成功");
    }

    @Override
    public List<DutyCalcOrderConfigVO> getDutyCalcOrderConfigs(Integer configId) {
        if (configId == null) {
            throw new GlobalBusinessException("配置ID不能为空");
        }

        // TODO: 实现获取责任理算顺序配置列表
        List<DutyCalcOrderConfigVO> dutyCalcOrderConfigVOs = new ArrayList<>();
        return dutyCalcOrderConfigVOs;
    }

    @Override
    @Transactional
    public void saveDutyCalcOrderConfig(List<DutyCalcOrderConfigVO> dutyCalcOrderConfigVOs, String userUM) {
        if (CollectionUtils.isEmpty(dutyCalcOrderConfigVOs)) {
            throw new GlobalBusinessException("责任理算顺序配置信息不能为空");
        }

        if (StringUtils.isBlank(userUM)) {
            throw new GlobalBusinessException("操作用户不能为空");
        }

        // TODO: 实现责任理算顺序配置保存逻辑

        LogUtil.audit("保存责任理算顺序配置成功");
    }

    @Override
    public List<VerifyRuleVO> getAvailableVerifyRules() {
        // TODO: 实现获取可用的核责规则列表
        List<VerifyRuleVO> verifyRuleVOs = new ArrayList<>();
        return verifyRuleVOs;
    }

    @Override
    public List<HospitalVO> searchHospitals(String hospitalName, String orgType) {
        if (StringUtils.isBlank(hospitalName)) {
            throw new GlobalBusinessException("医院名称不能为空");
        }

        // TODO: 实现医院搜索逻辑
        List<HospitalVO> hospitalVOs = new ArrayList<>();
        return hospitalVOs;
    }

    @Override
    public List<DiseaseIcdVO> searchDiseaseIcds(String searchStr, String diagnosisCategoryCode) {
        if (StringUtils.isBlank(searchStr)) {
            throw new GlobalBusinessException("搜索关键字不能为空");
        }

        // TODO: 实现疾病ICD搜索逻辑
        List<DiseaseIcdVO> diseaseIcdVOs = new ArrayList<>();
        return diseaseIcdVOs;
    }

    @Override
    public List<MedicineVO> searchMedicines(String medicineName, Integer packageId) {
        if (StringUtils.isBlank(medicineName) && packageId == null) {
            throw new GlobalBusinessException("药品名称或药品包ID不能同时为空");
        }

        // TODO: 实现药品搜索逻辑
        List<MedicineVO> medicineVOs = new ArrayList<>();
        return medicineVOs;
    }

    @Override
    public List<ExcludeRegionVO> getRegionList() {
        // TODO: 实现获取省市区列表
        List<ExcludeRegionVO> excludeRegionVOs = new ArrayList<>();
        return excludeRegionVOs;
    }
}
