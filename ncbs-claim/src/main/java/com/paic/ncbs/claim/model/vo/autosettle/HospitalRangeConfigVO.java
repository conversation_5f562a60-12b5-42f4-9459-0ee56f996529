package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 医院范围配置VO
 */
@ApiModel("医院范围配置VO")
@Getter
@Setter
public class HospitalRangeConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 核责事项
     */
    @ApiModelProperty("核责事项")
    private String verifyItem = "医院范围";

    /**
     * 医院模板ID
     */
    @ApiModelProperty("医院模板ID")
    private Integer hospitalTemplateId;

    /**
     * 医院模板名称
     */
    @ApiModelProperty("医院模板名称")
    private String hospitalTemplateName;

    /**
     * 医院等级
     */
    @ApiModelProperty("医院等级")
    private List<String> hospitalLevels;

    /**
     * 医院级别
     */
    @ApiModelProperty("医院级别")
    private List<String> hospitalGrades;

    /**
     * 医院性质
     */
    @ApiModelProperty("医院性质")
    private List<String> hospitalNatures;

    /**
     * 就诊分类
     */
    @ApiModelProperty("就诊分类")
    private List<String> medicalConsultations;

    /**
     * 医院区域
     */
    @ApiModelProperty("医院区域")
    private String hospitalRegion;

    /**
     * 除外省市区域列表
     */
    @ApiModelProperty("除外省市区域列表")
    private List<ExcludeRegionVO> excludeRegions;

    /**
     * 协议扩展医院列表
     */
    @ApiModelProperty("协议扩展医院列表")
    private List<HospitalVO> includeHospitals;

    /**
     * 除外医院列表
     */
    @ApiModelProperty("除外医院列表")
    private List<HospitalVO> excludeHospitals;
}
