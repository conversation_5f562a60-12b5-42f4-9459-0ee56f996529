package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 医院信息VO
 */
@ApiModel("医院信息VO")
@Getter
@Setter
public class HospitalVO {

    /**
     * 医院代码
     */
    @ApiModelProperty("医院代码")
    private String hospitalCode;

    /**
     * 医院名称
     */
    @ApiModelProperty("医院名称")
    private String hospitalName;

    /**
     * 医院等级
     */
    @ApiModelProperty("医院等级")
    private String hospitalLevel;

    /**
     * 医院级别
     */
    @ApiModelProperty("医院级别")
    private String hospitalGrade;

    /**
     * 医院性质
     */
    @ApiModelProperty("医院性质")
    private String hospitalNature;

    /**
     * 省份
     */
    @ApiModelProperty("省份")
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty("城市")
    private String city;

    /**
     * 区域
     */
    @ApiModelProperty("区域")
    private String region;
}
