package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 三目录除外配置VO
 */
@ApiModel("三目录除外配置VO")
@Getter
@Setter
public class Catalog3ExcludeConfigVO {

    /**
     * 配置ID
     */
    @ApiModelProperty("配置ID")
    private Integer id;

    /**
     * 方案名称
     */
    @ApiModelProperty("方案名称")
    private String riskGroupName;

    /**
     * 险种名称
     */
    @ApiModelProperty("险种名称")
    private String planName;

    /**
     * 责任名称
     */
    @ApiModelProperty("责任名称")
    private String dutyName;

    /**
     * 责任明细名称
     */
    @ApiModelProperty("责任明细名称")
    private String dutyDetailName;

    /**
     * 核责事项
     */
    @ApiModelProperty("核责事项")
    private String verifyItem = "三目录除外";

    /**
     * 除外药品配置列表
     */
    @ApiModelProperty("除外药品配置列表")
    private List<MedicineExcludeConfigVO> excludeMedicines;

    /**
     * 除外诊疗配置列表（暂不考虑）
     */
    @ApiModelProperty("除外诊疗配置列表")
    private List<TreatmentExcludeConfigVO> excludeTreatments;

    /**
     * 除外器材配置列表（暂不考虑）
     */
    @ApiModelProperty("除外器材配置列表")
    private List<MaterialExcludeConfigVO> excludeMaterials;
}
