package com.paic.ncbs.claim.dao.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 条款核责配置-疾病范围目录表
 */
@ApiModel("条款核责配置-疾病范围目录表")
@Getter
@Setter
public class ClmsVerifyDiseaseDir {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Integer id;

    /**
     * 外键
     */
    @ApiModelProperty("外键")
    private Integer configId;

    /**
     * 核责疾病范围id
     */
    @ApiModelProperty("核责疾病范围id")
    private Integer vDiseaseId;

    /**
     * 涵盖疾病类别
     */
    @ApiModelProperty("涵盖疾病类别")
    private String diseaseType;

    /**
     * 一级疾病目录id
     */
    @ApiModelProperty("一级疾病目录id")
    private Integer dir1Id;

    /**
     * 一级疾病目录名称
     */
    @ApiModelProperty("一级疾病目录名称")
    private String dir1Name;

    /**
     * 二级疾病目录id
     */
    @ApiModelProperty("二级疾病目录id")
    private String dir2Id;

    /**
     * 二级疾病目录名称
     */
    @ApiModelProperty("二级疾病目录名称")
    private String dir2Name;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date sysCtime;

    /**
     * 最新修改人员
     */
    @ApiModelProperty("最新修改人员")
    private String updatedBy;

    /**
     * 最新修改时间
     */
    @ApiModelProperty("最新修改时间")
    private Date sysUtime;

}
