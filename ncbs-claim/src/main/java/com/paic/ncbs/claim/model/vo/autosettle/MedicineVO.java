package com.paic.ncbs.claim.model.vo.autosettle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 药品信息VO
 */
@ApiModel("药品信息VO")
@Getter
@Setter
public class MedicineVO {

    /**
     * 药品代码
     */
    @ApiModelProperty("药品代码")
    private String medicineCode;

    /**
     * 药品名称
     */
    @ApiModelProperty("药品名称")
    private String medicineName;

    /**
     * 药品分类
     */
    @ApiModelProperty("药品分类")
    private String medicineCategory;

    /**
     * 药品规格
     */
    @ApiModelProperty("药品规格")
    private String medicineSpec;

    /**
     * 是否排除（不在除外范围内）
     */
    @ApiModelProperty("是否排除")
    private Boolean excluded;
}
