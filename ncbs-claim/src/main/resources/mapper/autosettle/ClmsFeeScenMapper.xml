<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsFeeScenMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsFeeScen">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="config_level" jdbcType="TINYINT" property="configLevel" />
        <result column="scen_type" jdbcType="VARCHAR" property="scenType" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
        <result column="plan_name" jdbcType="VARCHAR" property="planName" />
        <result column="risk_group_code" jdbcType="VARCHAR" property="riskGroupCode" />
        <result column="risk_group_name" jdbcType="VARCHAR" property="riskGroupName" />
        <result column="duty_code" jdbcType="VARCHAR" property="dutyCode" />
        <result column="duty_name" jdbcType="VARCHAR" property="dutyName" />
        <result column="duty_detail_code" jdbcType="VARCHAR" property="dutyDetailCode" />
        <result column="duty_detail_name" jdbcType="VARCHAR" property="dutyDetailName" />
        <result column="insurance_type_code" jdbcType="VARCHAR" property="insuranceTypeCode" />
        <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
        <result column="cost_codes" jdbcType="VARCHAR" property="costCodes" />
        <result column="detail_value" jdbcType="LONGVARCHAR" property="detailValue" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, config_level, scen_type, product_code, product_name, plan_code, plan_name, risk_group_code, risk_group_name, duty_code, duty_name, duty_detail_code, duty_detail_name, insurance_type_code, visit_type, cost_codes, detail_value, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_FEE_SCEN
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsFeeScen" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_FEE_SCEN (id, config_id, config_level, scen_type, product_code, product_name, plan_code, plan_name, risk_group_code, risk_group_name, duty_code, duty_name, duty_detail_code, duty_detail_name, insurance_type_code, visit_type, cost_codes, detail_value, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{configLevel,jdbcType=TINYINT}, #{scenType,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{planName,jdbcType=VARCHAR}, #{riskGroupCode,jdbcType=VARCHAR}, #{riskGroupName,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR}, #{dutyDetailCode,jdbcType=VARCHAR}, #{dutyDetailName,jdbcType=VARCHAR}, #{insuranceTypeCode,jdbcType=VARCHAR}, #{visitType,jdbcType=VARCHAR}, #{costCodes,jdbcType=VARCHAR}, #{detailValue,jdbcType=LONGVARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsFeeScen" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_FEE_SCEN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="configLevel != null">
                config_level,
            </if>
            <if test="scenType != null">
                scen_type,
            </if>
            <if test="productCode != null">
                product_code,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="planCode != null">
                plan_code,
            </if>
            <if test="planName != null">
                plan_name,
            </if>
            <if test="riskGroupCode != null">
                risk_group_code,
            </if>
            <if test="riskGroupName != null">
                risk_group_name,
            </if>
            <if test="dutyCode != null">
                duty_code,
            </if>
            <if test="dutyName != null">
                duty_name,
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code,
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name,
            </if>
            <if test="insuranceTypeCode != null">
                insurance_type_code,
            </if>
            <if test="visitType != null">
                visit_type,
            </if>
            <if test="costCodes != null">
                cost_codes,
            </if>
            <if test="detailValue != null">
                detail_value,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="configLevel != null">
                #{configLevel,jdbcType=TINYINT},
            </if>
            <if test="scenType != null">
                #{scenType,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                #{planName,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="insuranceTypeCode != null">
                #{insuranceTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="visitType != null">
                #{visitType,jdbcType=VARCHAR},
            </if>
            <if test="costCodes != null">
                #{costCodes,jdbcType=VARCHAR},
            </if>
            <if test="detailValue != null">
                #{detailValue,jdbcType=LONGVARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsFeeScen">
        update CLMS_FEE_SCEN
        set id = #{id,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            config_level = #{configLevel,jdbcType=TINYINT},
            scen_type = #{scenType,jdbcType=VARCHAR},
            product_code = #{productCode,jdbcType=VARCHAR},
            product_name = #{productName,jdbcType=VARCHAR},
            plan_code = #{planCode,jdbcType=VARCHAR},
            plan_name = #{planName,jdbcType=VARCHAR},
            risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            duty_code = #{dutyCode,jdbcType=VARCHAR},
            duty_name = #{dutyName,jdbcType=VARCHAR},
            duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            insurance_type_code = #{insuranceTypeCode,jdbcType=VARCHAR},
            visit_type = #{visitType,jdbcType=VARCHAR},
            cost_codes = #{costCodes,jdbcType=VARCHAR},
            detail_value = #{detailValue,jdbcType=LONGVARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsFeeScen">
        update CLMS_FEE_SCEN
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="configLevel != null">
                config_level = #{configLevel,jdbcType=TINYINT},
            </if>
            <if test="scenType != null">
                scen_type = #{scenType,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                product_code = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                plan_code = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                plan_name = #{planName,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                duty_code = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                duty_name = #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="insuranceTypeCode != null">
                insurance_type_code = #{insuranceTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="visitType != null">
                visit_type = #{visitType,jdbcType=VARCHAR},
            </if>
            <if test="costCodes != null">
                cost_codes = #{costCodes,jdbcType=VARCHAR},
            </if>
            <if test="detailValue != null">
                detail_value = #{detailValue,jdbcType=LONGVARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_FEE_SCEN
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_FEE_SCEN
    </select>

</mapper>
