<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsVerifyOtherMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOther">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="verify_level" jdbcType="TINYINT" property="verifyLevel" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
        <result column="plan_name" jdbcType="VARCHAR" property="planName" />
        <result column="risk_group_code" jdbcType="VARCHAR" property="riskGroupCode" />
        <result column="risk_group_name" jdbcType="VARCHAR" property="riskGroupName" />
        <result column="duty_code" jdbcType="VARCHAR" property="dutyCode" />
        <result column="duty_name" jdbcType="VARCHAR" property="dutyName" />
        <result column="duty_detail_code" jdbcType="VARCHAR" property="dutyDetailCode" />
        <result column="duty_detail_name" jdbcType="VARCHAR" property="dutyDetailName" />
        <result column="verify_items" jdbcType="VARCHAR" property="verifyItems" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, verify_level, product_code, product_name, plan_code, plan_name, risk_group_code, risk_group_name, duty_code, duty_name, duty_detail_code, duty_detail_name, verify_items, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_OTHER
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOther" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_OTHER (id, config_id, verify_level, product_code, product_name, plan_code, plan_name, risk_group_code, risk_group_name, duty_code, duty_name, duty_detail_code, duty_detail_name, verify_items, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{verifyLevel,jdbcType=TINYINT}, #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{planName,jdbcType=VARCHAR}, #{riskGroupCode,jdbcType=VARCHAR}, #{riskGroupName,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR}, #{dutyDetailCode,jdbcType=VARCHAR}, #{dutyDetailName,jdbcType=VARCHAR}, #{verifyItems,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOther" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_OTHER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="verifyLevel != null">
                verify_level,
            </if>
            <if test="productCode != null">
                product_code,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="planCode != null">
                plan_code,
            </if>
            <if test="planName != null">
                plan_name,
            </if>
            <if test="riskGroupCode != null">
                risk_group_code,
            </if>
            <if test="riskGroupName != null">
                risk_group_name,
            </if>
            <if test="dutyCode != null">
                duty_code,
            </if>
            <if test="dutyName != null">
                duty_name,
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code,
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name,
            </if>
            <if test="verifyItems != null">
                verify_items,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="verifyLevel != null">
                #{verifyLevel,jdbcType=TINYINT},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                #{planName,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="verifyItems != null">
                #{verifyItems,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOther">
        update CLMS_VERIFY_OTHER
        set id = #{id,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            verify_level = #{verifyLevel,jdbcType=TINYINT},
            product_code = #{productCode,jdbcType=VARCHAR},
            product_name = #{productName,jdbcType=VARCHAR},
            plan_code = #{planCode,jdbcType=VARCHAR},
            plan_name = #{planName,jdbcType=VARCHAR},
            risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            duty_code = #{dutyCode,jdbcType=VARCHAR},
            duty_name = #{dutyName,jdbcType=VARCHAR},
            duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            verify_items = #{verifyItems,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOther">
        update CLMS_VERIFY_OTHER
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="verifyLevel != null">
                verify_level = #{verifyLevel,jdbcType=TINYINT},
            </if>
            <if test="productCode != null">
                product_code = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                plan_code = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                plan_name = #{planName,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                duty_code = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                duty_name = #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="verifyItems != null">
                verify_items = #{verifyItems,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_VERIFY_OTHER
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_OTHER
    </select>

</mapper>
