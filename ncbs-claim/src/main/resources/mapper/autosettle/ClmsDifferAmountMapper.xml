<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsDifferAmountMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmount">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="model_name" jdbcType="VARCHAR" property="modelName" />
        <result column="differ_amount" jdbcType="DECIMAL" property="differAmount" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, model_name, differ_amount, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_DIFFER_AMOUNT
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmount" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_DIFFER_AMOUNT (id, model_name, differ_amount, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, #{differAmount,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmount" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_DIFFER_AMOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="modelName != null">
                model_name,
            </if>
            <if test="differAmount != null">
                differ_amount,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="modelName != null">
                #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="differAmount != null">
                #{differAmount,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmount">
        update CLMS_DIFFER_AMOUNT
        set id = #{id,jdbcType=VARCHAR},
            model_name = #{modelName,jdbcType=VARCHAR},
            differ_amount = #{differAmount,jdbcType=DECIMAL},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmount">
        update CLMS_DIFFER_AMOUNT
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="modelName != null">
                model_name = #{modelName,jdbcType=VARCHAR},
            </if>
            <if test="differAmount != null">
                differ_amount = #{differAmount,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_DIFFER_AMOUNT
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_DIFFER_AMOUNT
    </select>

</mapper>
