<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsVerifyCatalog3PackMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyCatalog3Pack">
        <result column="id" jdbcType="INTEGER" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="catalog3_id" jdbcType="INTEGER" property="catalog3Id" />
        <result column="package_id" jdbcType="INTEGER" property="packageId" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, catalog3_id, package_id, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_CATALOG3_PACK
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyCatalog3Pack" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_CATALOG3_PACK (id, config_id, catalog3_id, package_id, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=INTEGER}, #{configId,jdbcType=INTEGER}, #{catalog3Id,jdbcType=INTEGER}, #{packageId,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyCatalog3Pack" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_CATALOG3_PACK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="catalog3Id != null">
                catalog3_id,
            </if>
            <if test="packageId != null">
                package_id,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="catalog3Id != null">
                #{catalog3Id,jdbcType=INTEGER},
            </if>
            <if test="packageId != null">
                #{packageId,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyCatalog3Pack">
        update CLMS_VERIFY_CATALOG3_PACK
        set config_id = #{configId,jdbcType=INTEGER},
            catalog3_id = #{catalog3Id,jdbcType=INTEGER},
            package_id = #{packageId,jdbcType=INTEGER},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyCatalog3Pack">
        update CLMS_VERIFY_CATALOG3_PACK
        <set>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="catalog3Id != null">
                catalog3_id = #{catalog3Id,jdbcType=INTEGER},
            </if>
            <if test="packageId != null">
                package_id = #{packageId,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_VERIFY_CATALOG3_PACK
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_CATALOG3_PACK
    </select>

</mapper>