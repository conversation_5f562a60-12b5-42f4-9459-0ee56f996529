<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsVerifyOtherItemMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOtherItem">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="verify_type" jdbcType="VARCHAR" property="verifyType" />
        <result column="verify_name" jdbcType="VARCHAR" property="verifyName" />
        <result column="verify_level" jdbcType="VARCHAR" property="verifyLevel" />
        <result column="impl_class" jdbcType="VARCHAR" property="implClass" />
        <result column="process_flag" jdbcType="VARCHAR" property="processFlag" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, verify_type, verify_name, verify_level, impl_class, process_flag, remark, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_OTHER_ITEM
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOtherItem" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_OTHER_ITEM (id, verify_type, verify_name, verify_level, impl_class, process_flag, remark, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{verifyType,jdbcType=VARCHAR}, #{verifyName,jdbcType=VARCHAR}, #{verifyLevel,jdbcType=VARCHAR}, #{implClass,jdbcType=VARCHAR}, #{processFlag,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOtherItem" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_OTHER_ITEM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="verifyType != null">
                verify_type,
            </if>
            <if test="verifyName != null">
                verify_name,
            </if>
            <if test="verifyLevel != null">
                verify_level,
            </if>
            <if test="implClass != null">
                impl_class,
            </if>
            <if test="processFlag != null">
                process_flag,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="verifyType != null">
                #{verifyType,jdbcType=VARCHAR},
            </if>
            <if test="verifyName != null">
                #{verifyName,jdbcType=VARCHAR},
            </if>
            <if test="verifyLevel != null">
                #{verifyLevel,jdbcType=VARCHAR},
            </if>
            <if test="implClass != null">
                #{implClass,jdbcType=VARCHAR},
            </if>
            <if test="processFlag != null">
                #{processFlag,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOtherItem">
        update CLMS_VERIFY_OTHER_ITEM
        set id = #{id,jdbcType=VARCHAR},
            verify_type = #{verifyType,jdbcType=VARCHAR},
            verify_name = #{verifyName,jdbcType=VARCHAR},
            verify_level = #{verifyLevel,jdbcType=VARCHAR},
            impl_class = #{implClass,jdbcType=VARCHAR},
            process_flag = #{processFlag,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifyOtherItem">
        update CLMS_VERIFY_OTHER_ITEM
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="verifyType != null">
                verify_type = #{verifyType,jdbcType=VARCHAR},
            </if>
            <if test="verifyName != null">
                verify_name = #{verifyName,jdbcType=VARCHAR},
            </if>
            <if test="verifyLevel != null">
                verify_level = #{verifyLevel,jdbcType=VARCHAR},
            </if>
            <if test="implClass != null">
                impl_class = #{implClass,jdbcType=VARCHAR},
            </if>
            <if test="processFlag != null">
                process_flag = #{processFlag,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_VERIFY_OTHER_ITEM
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_OTHER_ITEM
    </select>

</mapper>
