<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseConfigMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfig">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
        <result column="plan_name" jdbcType="VARCHAR" property="planName" />
        <result column="pf_version" jdbcType="VARCHAR" property="pfVersion" />
        <result column="risk_group_code" jdbcType="VARCHAR" property="riskGroupCode" />
        <result column="risk_group_name" jdbcType="VARCHAR" property="riskGroupName" />
        <result column="version_no" jdbcType="INTEGER" property="versionNo" />
        <result column="define_source" jdbcType="CHAR" property="defineSource" />
        <result column="duty_detail" jdbcType="LONGVARCHAR" property="dutyDetail" />
        <result column="ig_flag" jdbcType="CHAR" property="igFlag" />
        <result column="valid_flag" jdbcType="VARCHAR" property="validFlag" />
        <result column="effect_time" jdbcType="TIMESTAMP" property="effectTime" />
        <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
        <result column="add_reason" jdbcType="VARCHAR" property="addReason" />
        <result column="add_reason_desc" jdbcType="VARCHAR" property="addReasonDesc" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, product_code, product_name, plan_code, plan_name, pf_version, risk_group_code, risk_group_name, version_no, define_source, duty_detail, ig_flag, valid_flag, effect_time, expire_time, add_reason, add_reason_desc, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CLAUSE_CONFIG
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfig" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CLAUSE_CONFIG (id, product_code, product_name, plan_code, plan_name, pf_version, risk_group_code, risk_group_name, version_no, define_source, duty_detail, ig_flag, valid_flag, effect_time, expire_time, add_reason, add_reason_desc, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{planName,jdbcType=VARCHAR}, #{pfVersion,jdbcType=VARCHAR}, #{riskGroupCode,jdbcType=VARCHAR}, #{riskGroupName,jdbcType=VARCHAR}, #{versionNo,jdbcType=INTEGER}, #{defineSource,jdbcType=CHAR}, #{dutyDetail,jdbcType=LONGVARCHAR}, #{igFlag,jdbcType=CHAR}, #{validFlag,jdbcType=VARCHAR}, #{effectTime,jdbcType=TIMESTAMP}, #{expireTime,jdbcType=TIMESTAMP}, #{addReason,jdbcType=VARCHAR}, #{addReasonDesc,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfig" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CLAUSE_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="productCode != null">
                product_code,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="planCode != null">
                plan_code,
            </if>
            <if test="planName != null">
                plan_name,
            </if>
            <if test="pfVersion != null">
                pf_version,
            </if>
            <if test="riskGroupCode != null">
                risk_group_code,
            </if>
            <if test="riskGroupName != null">
                risk_group_name,
            </if>
            <if test="versionNo != null">
                version_no,
            </if>
            <if test="defineSource != null">
                define_source,
            </if>
            <if test="dutyDetail != null">
                duty_detail,
            </if>
            <if test="igFlag != null">
                ig_flag,
            </if>
            <if test="validFlag != null">
                valid_flag,
            </if>
            <if test="effectTime != null">
                effect_time,
            </if>
            <if test="expireTime != null">
                expire_time,
            </if>
            <if test="addReason != null">
                add_reason,
            </if>
            <if test="addReasonDesc != null">
                add_reason_desc,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                #{planName,jdbcType=VARCHAR},
            </if>
            <if test="pfVersion != null">
                #{pfVersion,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="versionNo != null">
                #{versionNo,jdbcType=INTEGER},
            </if>
            <if test="defineSource != null">
                #{defineSource,jdbcType=CHAR},
            </if>
            <if test="dutyDetail != null">
                #{dutyDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="igFlag != null">
                #{igFlag,jdbcType=CHAR},
            </if>
            <if test="validFlag != null">
                #{validFlag,jdbcType=VARCHAR},
            </if>
            <if test="effectTime != null">
                #{effectTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                #{expireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addReason != null">
                #{addReason,jdbcType=VARCHAR},
            </if>
            <if test="addReasonDesc != null">
                #{addReasonDesc,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfig">
        update CLMS_CLAUSE_CONFIG
        set id = #{id,jdbcType=VARCHAR},
            product_code = #{productCode,jdbcType=VARCHAR},
            product_name = #{productName,jdbcType=VARCHAR},
            plan_code = #{planCode,jdbcType=VARCHAR},
            plan_name = #{planName,jdbcType=VARCHAR},
            pf_version = #{pfVersion,jdbcType=VARCHAR},
            risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            version_no = #{versionNo,jdbcType=INTEGER},
            define_source = #{defineSource,jdbcType=CHAR},
            duty_detail = #{dutyDetail,jdbcType=LONGVARCHAR},
            ig_flag = #{igFlag,jdbcType=CHAR},
            valid_flag = #{validFlag,jdbcType=VARCHAR},
            effect_time = #{effectTime,jdbcType=TIMESTAMP},
            expire_time = #{expireTime,jdbcType=TIMESTAMP},
            add_reason = #{addReason,jdbcType=VARCHAR},
            add_reason_desc = #{addReasonDesc,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseConfig">
        update CLMS_CLAUSE_CONFIG
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                product_code = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                plan_code = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                plan_name = #{planName,jdbcType=VARCHAR},
            </if>
            <if test="pfVersion != null">
                pf_version = #{pfVersion,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="versionNo != null">
                version_no = #{versionNo,jdbcType=INTEGER},
            </if>
            <if test="defineSource != null">
                define_source = #{defineSource,jdbcType=CHAR},
            </if>
            <if test="dutyDetail != null">
                duty_detail = #{dutyDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="igFlag != null">
                ig_flag = #{igFlag,jdbcType=CHAR},
            </if>
            <if test="validFlag != null">
                valid_flag = #{validFlag,jdbcType=VARCHAR},
            </if>
            <if test="effectTime != null">
                effect_time = #{effectTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="addReason != null">
                add_reason = #{addReason,jdbcType=VARCHAR},
            </if>
            <if test="addReasonDesc != null">
                add_reason_desc = #{addReasonDesc,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_CLAUSE_CONFIG
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CLAUSE_CONFIG
    </select>

    <!-- 根据产品代码和方案代码查询有效的配置ID -->
    <select id="getEffectiveConfigId" resultType="java.lang.Integer">
        select id
        from CLMS_CLAUSE_CONFIG
        where product_code = #{productCode,jdbcType=VARCHAR}
        and plan_code = #{planCode,jdbcType=VARCHAR}
        and valid_flag = 'Y'
        and effect_time &lt;= SYSDATE
        and (expire_time is null or expire_time &gt; SYSDATE)
        order by version_no desc
        limit 1
    </select>

</mapper>
