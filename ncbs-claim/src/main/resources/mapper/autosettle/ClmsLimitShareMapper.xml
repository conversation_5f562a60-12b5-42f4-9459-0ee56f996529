<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsLimitShareMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsLimitShare">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="fee_config_id" jdbcType="INTEGER" property="feeConfigId" />
        <result column="share_type" jdbcType="VARCHAR" property="shareType" />
        <result column="times_month_define" jdbcType="VARCHAR" property="timesMonthDefine" />
        <result column="share_value" jdbcType="DECIMAL" property="shareValue" />
        <result column="times_define" jdbcType="VARCHAR" property="timesDefine" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, fee_config_id, share_type, times_month_define, share_value, times_define, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_LIMIT_SHARE
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsLimitShare" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_LIMIT_SHARE (id, config_id, fee_config_id, share_type, times_month_define, share_value, times_define, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{feeConfigId,jdbcType=INTEGER}, #{shareType,jdbcType=VARCHAR}, #{timesMonthDefine,jdbcType=VARCHAR}, #{shareValue,jdbcType=DECIMAL}, #{timesDefine,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsLimitShare" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_LIMIT_SHARE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="feeConfigId != null">
                fee_config_id,
            </if>
            <if test="shareType != null">
                share_type,
            </if>
            <if test="timesMonthDefine != null">
                times_month_define,
            </if>
            <if test="shareValue != null">
                share_value,
            </if>
            <if test="timesDefine != null">
                times_define,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="feeConfigId != null">
                #{feeConfigId,jdbcType=INTEGER},
            </if>
            <if test="shareType != null">
                #{shareType,jdbcType=VARCHAR},
            </if>
            <if test="timesMonthDefine != null">
                #{timesMonthDefine,jdbcType=VARCHAR},
            </if>
            <if test="shareValue != null">
                #{shareValue,jdbcType=DECIMAL},
            </if>
            <if test="timesDefine != null">
                #{timesDefine,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsLimitShare">
        update CLMS_LIMIT_SHARE
        set id = #{id,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            fee_config_id = #{feeConfigId,jdbcType=INTEGER},
            share_type = #{shareType,jdbcType=VARCHAR},
            times_month_define = #{timesMonthDefine,jdbcType=VARCHAR},
            share_value = #{shareValue,jdbcType=DECIMAL},
            times_define = #{timesDefine,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsLimitShare">
        update CLMS_LIMIT_SHARE
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="feeConfigId != null">
                fee_config_id = #{feeConfigId,jdbcType=INTEGER},
            </if>
            <if test="shareType != null">
                share_type = #{shareType,jdbcType=VARCHAR},
            </if>
            <if test="timesMonthDefine != null">
                times_month_define = #{timesMonthDefine,jdbcType=VARCHAR},
            </if>
            <if test="shareValue != null">
                share_value = #{shareValue,jdbcType=DECIMAL},
            </if>
            <if test="timesDefine != null">
                times_define = #{timesDefine,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_LIMIT_SHARE
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_LIMIT_SHARE
    </select>

</mapper>
