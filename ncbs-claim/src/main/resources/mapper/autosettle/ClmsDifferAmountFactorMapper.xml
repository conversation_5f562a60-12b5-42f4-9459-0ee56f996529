<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsDifferAmountFactorMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmountFactor">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="da_id" jdbcType="INTEGER" property="daId" />
        <result column="factor_code" jdbcType="VARCHAR" property="factorCode" />
        <result column="factor_name" jdbcType="VARCHAR" property="factorName" />
        <result column="famular" jdbcType="VARCHAR" property="famular" />
        <result column="factor_values" jdbcType="VARCHAR" property="factorValues" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, da_id, factor_code, factor_name, famular, factor_values, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_DIFFER_AMOUNT_FACTOR
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmountFactor" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_DIFFER_AMOUNT_FACTOR (id, da_id, factor_code, factor_name, famular, factor_values, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{daId,jdbcType=INTEGER}, #{factorCode,jdbcType=VARCHAR}, #{factorName,jdbcType=VARCHAR}, #{famular,jdbcType=VARCHAR}, #{factorValues,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmountFactor" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_DIFFER_AMOUNT_FACTOR
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="daId != null">
                da_id,
            </if>
            <if test="factorCode != null">
                factor_code,
            </if>
            <if test="factorName != null">
                factor_name,
            </if>
            <if test="famular != null">
                famular,
            </if>
            <if test="factorValues != null">
                factor_values,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="daId != null">
                #{daId,jdbcType=INTEGER},
            </if>
            <if test="factorCode != null">
                #{factorCode,jdbcType=VARCHAR},
            </if>
            <if test="factorName != null">
                #{factorName,jdbcType=VARCHAR},
            </if>
            <if test="famular != null">
                #{famular,jdbcType=VARCHAR},
            </if>
            <if test="factorValues != null">
                #{factorValues,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmountFactor">
        update CLMS_DIFFER_AMOUNT_FACTOR
        set id = #{id,jdbcType=VARCHAR},
            da_id = #{daId,jdbcType=INTEGER},
            factor_code = #{factorCode,jdbcType=VARCHAR},
            factor_name = #{factorName,jdbcType=VARCHAR},
            famular = #{famular,jdbcType=VARCHAR},
            factor_values = #{factorValues,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsDifferAmountFactor">
        update CLMS_DIFFER_AMOUNT_FACTOR
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="daId != null">
                da_id = #{daId,jdbcType=INTEGER},
            </if>
            <if test="factorCode != null">
                factor_code = #{factorCode,jdbcType=VARCHAR},
            </if>
            <if test="factorName != null">
                factor_name = #{factorName,jdbcType=VARCHAR},
            </if>
            <if test="famular != null">
                famular = #{famular,jdbcType=VARCHAR},
            </if>
            <if test="factorValues != null">
                factor_values = #{factorValues,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_DIFFER_AMOUNT_FACTOR
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_DIFFER_AMOUNT_FACTOR
    </select>

</mapper>
