<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsCaculateLogMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateLog">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="report_no" jdbcType="VARCHAR" property="reportNo" />
        <result column="case_times" jdbcType="INTEGER" property="caseTimes" />
        <result column="policy_no" jdbcType="VARCHAR" property="policyNo" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
        <result column="plan_name" jdbcType="VARCHAR" property="planName" />
        <result column="risk_group_code" jdbcType="VARCHAR" property="riskGroupCode" />
        <result column="risk_group_name" jdbcType="VARCHAR" property="riskGroupName" />
        <result column="calc_times" jdbcType="INTEGER" property="calcTimes" />
        <result column="sum_pay" jdbcType="DECIMAL" property="sumPay" />
        <result column="disclaim_sum_pay" jdbcType="DECIMAL" property="disclaimSumPay" />
        <result column="valid_flag" jdbcType="BOOLEAN" property="validFlag" />
        <result column="calc_according" jdbcType="LONGVARCHAR" property="calcAccording" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, report_no, case_times, policy_no, product_code, product_name, plan_code, plan_name, risk_group_code, risk_group_name, calc_times, sum_pay, disclaim_sum_pay, valid_flag, calc_according, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CACULATE_LOG
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateLog" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CACULATE_LOG (id, config_id, report_no, case_times, policy_no, product_code, product_name, plan_code, plan_name, risk_group_code, risk_group_name, calc_times, sum_pay, disclaim_sum_pay, valid_flag, calc_according, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{reportNo,jdbcType=VARCHAR}, #{caseTimes,jdbcType=INTEGER}, #{policyNo,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{planName,jdbcType=VARCHAR}, #{riskGroupCode,jdbcType=VARCHAR}, #{riskGroupName,jdbcType=VARCHAR}, #{calcTimes,jdbcType=INTEGER}, #{sumPay,jdbcType=DECIMAL}, #{disclaimSumPay,jdbcType=DECIMAL}, #{validFlag,jdbcType=BOOLEAN}, #{calcAccording,jdbcType=LONGVARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateLog" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CACULATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="reportNo != null">
                report_no,
            </if>
            <if test="caseTimes != null">
                case_times,
            </if>
            <if test="policyNo != null">
                policy_no,
            </if>
            <if test="productCode != null">
                product_code,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="planCode != null">
                plan_code,
            </if>
            <if test="planName != null">
                plan_name,
            </if>
            <if test="riskGroupCode != null">
                risk_group_code,
            </if>
            <if test="riskGroupName != null">
                risk_group_name,
            </if>
            <if test="calcTimes != null">
                calc_times,
            </if>
            <if test="sumPay != null">
                sum_pay,
            </if>
            <if test="disclaimSumPay != null">
                disclaim_sum_pay,
            </if>
            <if test="validFlag != null">
                valid_flag,
            </if>
            <if test="calcAccording != null">
                calc_according,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="reportNo != null">
                #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                #{caseTimes,jdbcType=INTEGER},
            </if>
            <if test="policyNo != null">
                #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                #{planName,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="calcTimes != null">
                #{calcTimes,jdbcType=INTEGER},
            </if>
            <if test="sumPay != null">
                #{sumPay,jdbcType=DECIMAL},
            </if>
            <if test="disclaimSumPay != null">
                #{disclaimSumPay,jdbcType=DECIMAL},
            </if>
            <if test="validFlag != null">
                #{validFlag,jdbcType=BOOLEAN},
            </if>
            <if test="calcAccording != null">
                #{calcAccording,jdbcType=LONGVARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateLog">
        update CLMS_CACULATE_LOG
        set id = #{id,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            report_no = #{reportNo,jdbcType=VARCHAR},
            case_times = #{caseTimes,jdbcType=INTEGER},
            policy_no = #{policyNo,jdbcType=VARCHAR},
            product_code = #{productCode,jdbcType=VARCHAR},
            product_name = #{productName,jdbcType=VARCHAR},
            plan_code = #{planCode,jdbcType=VARCHAR},
            plan_name = #{planName,jdbcType=VARCHAR},
            risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            calc_times = #{calcTimes,jdbcType=INTEGER},
            sum_pay = #{sumPay,jdbcType=DECIMAL},
            disclaim_sum_pay = #{disclaimSumPay,jdbcType=DECIMAL},
            valid_flag = #{validFlag,jdbcType=BOOLEAN},
            calc_according = #{calcAccording,jdbcType=LONGVARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateLog">
        update CLMS_CACULATE_LOG
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="reportNo != null">
                report_no = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="caseTimes != null">
                case_times = #{caseTimes,jdbcType=INTEGER},
            </if>
            <if test="policyNo != null">
                policy_no = #{policyNo,jdbcType=VARCHAR},
            </if>
            <if test="productCode != null">
                product_code = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                plan_code = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                plan_name = #{planName,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="calcTimes != null">
                calc_times = #{calcTimes,jdbcType=INTEGER},
            </if>
            <if test="sumPay != null">
                sum_pay = #{sumPay,jdbcType=DECIMAL},
            </if>
            <if test="disclaimSumPay != null">
                disclaim_sum_pay = #{disclaimSumPay,jdbcType=DECIMAL},
            </if>
            <if test="validFlag != null">
                valid_flag = #{validFlag,jdbcType=BOOLEAN},
            </if>
            <if test="calcAccording != null">
                calc_according = #{calcAccording,jdbcType=LONGVARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_CACULATE_LOG
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CACULATE_LOG
    </select>

</mapper>
