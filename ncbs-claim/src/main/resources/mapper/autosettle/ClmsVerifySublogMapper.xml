<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsVerifySublogMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySublog">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="verify_log_id" jdbcType="INTEGER" property="verifyLogId" />
        <result column="verify_level" jdbcType="VARCHAR" property="verifyLevel" />
        <result column="verify_class" jdbcType="VARCHAR" property="verifyClass" />
        <result column="id_ahcs_invoice_info" jdbcType="VARCHAR" property="idAhcsInvoiceInfo" />
        <result column="duty_code" jdbcType="VARCHAR" property="dutyCode" />
        <result column="duty_name" jdbcType="VARCHAR" property="dutyName" />
        <result column="duty_detail_code" jdbcType="VARCHAR" property="dutyDetailCode" />
        <result column="duty_detail_name" jdbcType="VARCHAR" property="dutyDetailName" />
        <result column="invoice_item_type" jdbcType="VARCHAR" property="invoiceItemType" />
        <result column="invoice_item_code" jdbcType="VARCHAR" property="invoiceItemCode" />
        <result column="invoice_detail_name" jdbcType="VARCHAR" property="invoiceDetailName" />
        <result column="disclaim" jdbcType="BOOLEAN" property="disclaim" />
        <result column="disclaim_desc" jdbcType="VARCHAR" property="disclaimDesc" />
        <result column="disclaim_value" jdbcType="DECIMAL" property="disclaimValue" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, verify_log_id, verify_level, verify_class, id_ahcs_invoice_info, duty_code, duty_name, duty_detail_code, duty_detail_name, invoice_item_type, invoice_item_code, invoice_detail_name, disclaim, disclaim_desc, disclaim_value, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_SUBLOG
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySublog" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_SUBLOG (id, verify_log_id, verify_level, verify_class, id_ahcs_invoice_info, duty_code, duty_name, duty_detail_code, duty_detail_name, invoice_item_type, invoice_item_code, invoice_detail_name, disclaim, disclaim_desc, disclaim_value, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{verifyLogId,jdbcType=INTEGER}, #{verifyLevel,jdbcType=VARCHAR}, #{verifyClass,jdbcType=VARCHAR}, #{idAhcsInvoiceInfo,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR}, #{dutyDetailCode,jdbcType=VARCHAR}, #{dutyDetailName,jdbcType=VARCHAR}, #{invoiceItemType,jdbcType=VARCHAR}, #{invoiceItemCode,jdbcType=VARCHAR}, #{invoiceDetailName,jdbcType=VARCHAR}, #{disclaim,jdbcType=BOOLEAN}, #{disclaimDesc,jdbcType=VARCHAR}, #{disclaimValue,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySublog" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_SUBLOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="verifyLogId != null">
                verify_log_id,
            </if>
            <if test="verifyLevel != null">
                verify_level,
            </if>
            <if test="verifyClass != null">
                verify_class,
            </if>
            <if test="idAhcsInvoiceInfo != null">
                id_ahcs_invoice_info,
            </if>
            <if test="dutyCode != null">
                duty_code,
            </if>
            <if test="dutyName != null">
                duty_name,
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code,
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name,
            </if>
            <if test="invoiceItemType != null">
                invoice_item_type,
            </if>
            <if test="invoiceItemCode != null">
                invoice_item_code,
            </if>
            <if test="invoiceDetailName != null">
                invoice_detail_name,
            </if>
            <if test="disclaim != null">
                disclaim,
            </if>
            <if test="disclaimDesc != null">
                disclaim_desc,
            </if>
            <if test="disclaimValue != null">
                disclaim_value,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="verifyLogId != null">
                #{verifyLogId,jdbcType=INTEGER},
            </if>
            <if test="verifyLevel != null">
                #{verifyLevel,jdbcType=VARCHAR},
            </if>
            <if test="verifyClass != null">
                #{verifyClass,jdbcType=VARCHAR},
            </if>
            <if test="idAhcsInvoiceInfo != null">
                #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemType != null">
                #{invoiceItemType,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemCode != null">
                #{invoiceItemCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceDetailName != null">
                #{invoiceDetailName,jdbcType=VARCHAR},
            </if>
            <if test="disclaim != null">
                #{disclaim,jdbcType=BOOLEAN},
            </if>
            <if test="disclaimDesc != null">
                #{disclaimDesc,jdbcType=VARCHAR},
            </if>
            <if test="disclaimValue != null">
                #{disclaimValue,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySublog">
        update CLMS_VERIFY_SUBLOG
        set id = #{id,jdbcType=VARCHAR},
            verify_log_id = #{verifyLogId,jdbcType=INTEGER},
            verify_level = #{verifyLevel,jdbcType=VARCHAR},
            verify_class = #{verifyClass,jdbcType=VARCHAR},
            id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            duty_code = #{dutyCode,jdbcType=VARCHAR},
            duty_name = #{dutyName,jdbcType=VARCHAR},
            duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            invoice_item_type = #{invoiceItemType,jdbcType=VARCHAR},
            invoice_item_code = #{invoiceItemCode,jdbcType=VARCHAR},
            invoice_detail_name = #{invoiceDetailName,jdbcType=VARCHAR},
            disclaim = #{disclaim,jdbcType=BOOLEAN},
            disclaim_desc = #{disclaimDesc,jdbcType=VARCHAR},
            disclaim_value = #{disclaimValue,jdbcType=DECIMAL},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySublog">
        update CLMS_VERIFY_SUBLOG
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="verifyLogId != null">
                verify_log_id = #{verifyLogId,jdbcType=INTEGER},
            </if>
            <if test="verifyLevel != null">
                verify_level = #{verifyLevel,jdbcType=VARCHAR},
            </if>
            <if test="verifyClass != null">
                verify_class = #{verifyClass,jdbcType=VARCHAR},
            </if>
            <if test="idAhcsInvoiceInfo != null">
                id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                duty_code = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                duty_name = #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemType != null">
                invoice_item_type = #{invoiceItemType,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemCode != null">
                invoice_item_code = #{invoiceItemCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceDetailName != null">
                invoice_detail_name = #{invoiceDetailName,jdbcType=VARCHAR},
            </if>
            <if test="disclaim != null">
                disclaim = #{disclaim,jdbcType=BOOLEAN},
            </if>
            <if test="disclaimDesc != null">
                disclaim_desc = #{disclaimDesc,jdbcType=VARCHAR},
            </if>
            <if test="disclaimValue != null">
                disclaim_value = #{disclaimValue,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_VERIFY_SUBLOG
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_SUBLOG
    </select>

</mapper>
