<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsMedicinePackageDetailMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetail">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="package_id" jdbcType="INTEGER" property="packageId" />
        <result column="medicine_name" jdbcType="VARCHAR" property="medicineName" />
        <result column="medicine_category" jdbcType="VARCHAR" property="medicineCategory" />
        <result column="indication" jdbcType="VARCHAR" property="indication" />
        <result column="valid_flag" jdbcType="BOOLEAN" property="validFlag" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, package_id, medicine_name, medicine_category, indication, valid_flag, remark, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_MEDICINE_PACKAGE_DETAIL
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetail" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_MEDICINE_PACKAGE_DETAIL (id, package_id, medicine_name, medicine_category, indication, valid_flag, remark, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{packageId,jdbcType=INTEGER}, #{medicineName,jdbcType=VARCHAR}, #{medicineCategory,jdbcType=VARCHAR}, #{indication,jdbcType=VARCHAR}, #{validFlag,jdbcType=BOOLEAN}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetail" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_MEDICINE_PACKAGE_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="packageId != null">
                package_id,
            </if>
            <if test="medicineName != null">
                medicine_name,
            </if>
            <if test="medicineCategory != null">
                medicine_category,
            </if>
            <if test="indication != null">
                indication,
            </if>
            <if test="validFlag != null">
                valid_flag,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="packageId != null">
                #{packageId,jdbcType=INTEGER},
            </if>
            <if test="medicineName != null">
                #{medicineName,jdbcType=VARCHAR},
            </if>
            <if test="medicineCategory != null">
                #{medicineCategory,jdbcType=VARCHAR},
            </if>
            <if test="indication != null">
                #{indication,jdbcType=VARCHAR},
            </if>
            <if test="validFlag != null">
                #{validFlag,jdbcType=BOOLEAN},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetail">
        update CLMS_MEDICINE_PACKAGE_DETAIL
        set id = #{id,jdbcType=VARCHAR},
            package_id = #{packageId,jdbcType=INTEGER},
            medicine_name = #{medicineName,jdbcType=VARCHAR},
            medicine_category = #{medicineCategory,jdbcType=VARCHAR},
            indication = #{indication,jdbcType=VARCHAR},
            valid_flag = #{validFlag,jdbcType=BOOLEAN},
            remark = #{remark,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsMedicinePackageDetail">
        update CLMS_MEDICINE_PACKAGE_DETAIL
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="packageId != null">
                package_id = #{packageId,jdbcType=INTEGER},
            </if>
            <if test="medicineName != null">
                medicine_name = #{medicineName,jdbcType=VARCHAR},
            </if>
            <if test="medicineCategory != null">
                medicine_category = #{medicineCategory,jdbcType=VARCHAR},
            </if>
            <if test="indication != null">
                indication = #{indication,jdbcType=VARCHAR},
            </if>
            <if test="validFlag != null">
                valid_flag = #{validFlag,jdbcType=BOOLEAN},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_MEDICINE_PACKAGE_DETAIL
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_MEDICINE_PACKAGE_DETAIL
    </select>

</mapper>
