<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsAllowanceScenMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsAllowanceScen">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="duty_code" jdbcType="VARCHAR" property="dutyCode" />
        <result column="duty_name" jdbcType="VARCHAR" property="dutyName" />
        <result column="duty_detail_code" jdbcType="VARCHAR" property="dutyDetailCode" />
        <result column="duty_detail_name" jdbcType="VARCHAR" property="dutyDetailName" />
        <result column="insurance_type_code" jdbcType="VARCHAR" property="insuranceTypeCode" />
        <result column="visit_type" jdbcType="VARCHAR" property="visitType" />
        <result column="detail_value" jdbcType="LONGVARCHAR" property="detailValue" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, duty_code, duty_name, duty_detail_code, duty_detail_name, insurance_type_code, visit_type, detail_value, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_ALLOWANCE_SCEN
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsAllowanceScen" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_ALLOWANCE_SCEN (id, config_id, duty_code, duty_name, duty_detail_code, duty_detail_name, insurance_type_code, visit_type, detail_value, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{dutyCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR}, #{dutyDetailCode,jdbcType=VARCHAR}, #{dutyDetailName,jdbcType=VARCHAR}, #{insuranceTypeCode,jdbcType=VARCHAR}, #{visitType,jdbcType=VARCHAR}, #{detailValue,jdbcType=LONGVARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsAllowanceScen" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_ALLOWANCE_SCEN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="dutyCode != null">
                duty_code,
            </if>
            <if test="dutyName != null">
                duty_name,
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code,
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name,
            </if>
            <if test="insuranceTypeCode != null">
                insurance_type_code,
            </if>
            <if test="visitType != null">
                visit_type,
            </if>
            <if test="detailValue != null">
                detail_value,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="insuranceTypeCode != null">
                #{insuranceTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="visitType != null">
                #{visitType,jdbcType=VARCHAR},
            </if>
            <if test="detailValue != null">
                #{detailValue,jdbcType=LONGVARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsAllowanceScen">
        update CLMS_ALLOWANCE_SCEN
        set id = #{id,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            duty_code = #{dutyCode,jdbcType=VARCHAR},
            duty_name = #{dutyName,jdbcType=VARCHAR},
            duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            insurance_type_code = #{insuranceTypeCode,jdbcType=VARCHAR},
            visit_type = #{visitType,jdbcType=VARCHAR},
            detail_value = #{detailValue,jdbcType=LONGVARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsAllowanceScen">
        update CLMS_ALLOWANCE_SCEN
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="dutyCode != null">
                duty_code = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                duty_name = #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="insuranceTypeCode != null">
                insurance_type_code = #{insuranceTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="visitType != null">
                visit_type = #{visitType,jdbcType=VARCHAR},
            </if>
            <if test="detailValue != null">
                detail_value = #{detailValue,jdbcType=LONGVARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_ALLOWANCE_SCEN
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_ALLOWANCE_SCEN
    </select>

</mapper>
