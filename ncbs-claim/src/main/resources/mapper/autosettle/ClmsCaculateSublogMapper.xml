<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsCaculateSublogMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateSublog">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="caculate_log_id" jdbcType="INTEGER" property="caculateLogId" />
        <result column="file_id" jdbcType="VARCHAR" property="fileId" />
        <result column="id_ahcs_invoice_info" jdbcType="VARCHAR" property="idAhcsInvoiceInfo" />
        <result column="scen_type" jdbcType="VARCHAR" property="scenType" />
        <result column="scen_id" jdbcType="INTEGER" property="scenId" />
        <result column="PAY_PROPORTION" jdbcType="DECIMAL" property="payProportion" />
        <result column="duty_code" jdbcType="VARCHAR" property="dutyCode" />
        <result column="duty_name" jdbcType="VARCHAR" property="dutyName" />
        <result column="duty_detail_code" jdbcType="VARCHAR" property="dutyDetailCode" />
        <result column="duty_detail_name" jdbcType="VARCHAR" property="dutyDetailName" />
        <result column="invoice_item_type" jdbcType="VARCHAR" property="invoiceItemType" />
        <result column="invoice_item_code" jdbcType="VARCHAR" property="invoiceItemCode" />
        <result column="invoice_item_name" jdbcType="VARCHAR" property="invoiceItemName" />
        <result column="invoice_detail_code" jdbcType="VARCHAR" property="invoiceDetailCode" />
        <result column="invoice_detail_name" jdbcType="VARCHAR" property="invoiceDetailName" />
        <result column="self_pay_amount" jdbcType="DECIMAL" property="selfPayAmount" />
        <result column="partial_self_pay_amount" jdbcType="DECIMAL" property="partialSelfPayAmount" />
        <result column="unreasonable_amount" jdbcType="DECIMAL" property="unreasonableAmount" />
        <result column="third_party_payment" jdbcType="DECIMAL" property="thirdPartyPayment" />
        <result column="reasonable_medical_expense" jdbcType="DECIMAL" property="reasonableMedicalExpense" />
        <result column="calculated_compensation_amount" jdbcType="DECIMAL" property="calculatedCompensationAmount" />
        <result column="deduction_amount" jdbcType="DECIMAL" property="deductionAmount" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, caculate_log_id, file_id, id_ahcs_invoice_info, scen_type, scen_id, PAY_PROPORTION, duty_code, duty_name, duty_detail_code, duty_detail_name, invoice_item_type, invoice_item_code, invoice_item_name, invoice_detail_code, invoice_detail_name, self_pay_amount, partial_self_pay_amount, unreasonable_amount, third_party_payment, reasonable_medical_expense, calculated_compensation_amount, deduction_amount, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CACULATE_SUBLOG
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateSublog" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CACULATE_SUBLOG (id, caculate_log_id, file_id, id_ahcs_invoice_info, scen_type, scen_id, PAY_PROPORTION, duty_code, duty_name, duty_detail_code, duty_detail_name, invoice_item_type, invoice_item_code, invoice_item_name, invoice_detail_code, invoice_detail_name, self_pay_amount, partial_self_pay_amount, unreasonable_amount, third_party_payment, reasonable_medical_expense, calculated_compensation_amount, deduction_amount, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{caculateLogId,jdbcType=INTEGER}, #{fileId,jdbcType=VARCHAR}, #{idAhcsInvoiceInfo,jdbcType=VARCHAR}, #{scenType,jdbcType=VARCHAR}, #{scenId,jdbcType=INTEGER}, #{payProportion,jdbcType=DECIMAL}, #{dutyCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR}, #{dutyDetailCode,jdbcType=VARCHAR}, #{dutyDetailName,jdbcType=VARCHAR}, #{invoiceItemType,jdbcType=VARCHAR}, #{invoiceItemCode,jdbcType=VARCHAR}, #{invoiceItemName,jdbcType=VARCHAR}, #{invoiceDetailCode,jdbcType=VARCHAR}, #{invoiceDetailName,jdbcType=VARCHAR}, #{selfPayAmount,jdbcType=DECIMAL}, #{partialSelfPayAmount,jdbcType=DECIMAL}, #{unreasonableAmount,jdbcType=DECIMAL}, #{thirdPartyPayment,jdbcType=DECIMAL}, #{reasonableMedicalExpense,jdbcType=DECIMAL}, #{calculatedCompensationAmount,jdbcType=DECIMAL}, #{deductionAmount,jdbcType=DECIMAL}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateSublog" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CACULATE_SUBLOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="caculateLogId != null">
                caculate_log_id,
            </if>
            <if test="fileId != null">
                file_id,
            </if>
            <if test="idAhcsInvoiceInfo != null">
                id_ahcs_invoice_info,
            </if>
            <if test="scenType != null">
                scen_type,
            </if>
            <if test="scenId != null">
                scen_id,
            </if>
            <if test="payProportion != null">
                PAY_PROPORTION,
            </if>
            <if test="dutyCode != null">
                duty_code,
            </if>
            <if test="dutyName != null">
                duty_name,
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code,
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name,
            </if>
            <if test="invoiceItemType != null">
                invoice_item_type,
            </if>
            <if test="invoiceItemCode != null">
                invoice_item_code,
            </if>
            <if test="invoiceItemName != null">
                invoice_item_name,
            </if>
            <if test="invoiceDetailCode != null">
                invoice_detail_code,
            </if>
            <if test="invoiceDetailName != null">
                invoice_detail_name,
            </if>
            <if test="selfPayAmount != null">
                self_pay_amount,
            </if>
            <if test="partialSelfPayAmount != null">
                partial_self_pay_amount,
            </if>
            <if test="unreasonableAmount != null">
                unreasonable_amount,
            </if>
            <if test="thirdPartyPayment != null">
                third_party_payment,
            </if>
            <if test="reasonableMedicalExpense != null">
                reasonable_medical_expense,
            </if>
            <if test="calculatedCompensationAmount != null">
                calculated_compensation_amount,
            </if>
            <if test="deductionAmount != null">
                deduction_amount,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="caculateLogId != null">
                #{caculateLogId,jdbcType=INTEGER},
            </if>
            <if test="fileId != null">
                #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="idAhcsInvoiceInfo != null">
                #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            </if>
            <if test="scenType != null">
                #{scenType,jdbcType=VARCHAR},
            </if>
            <if test="scenId != null">
                #{scenId,jdbcType=INTEGER},
            </if>
            <if test="payProportion != null">
                #{payProportion,jdbcType=DECIMAL},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemType != null">
                #{invoiceItemType,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemCode != null">
                #{invoiceItemCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemName != null">
                #{invoiceItemName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceDetailCode != null">
                #{invoiceDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceDetailName != null">
                #{invoiceDetailName,jdbcType=VARCHAR},
            </if>
            <if test="selfPayAmount != null">
                #{selfPayAmount,jdbcType=DECIMAL},
            </if>
            <if test="partialSelfPayAmount != null">
                #{partialSelfPayAmount,jdbcType=DECIMAL},
            </if>
            <if test="unreasonableAmount != null">
                #{unreasonableAmount,jdbcType=DECIMAL},
            </if>
            <if test="thirdPartyPayment != null">
                #{thirdPartyPayment,jdbcType=DECIMAL},
            </if>
            <if test="reasonableMedicalExpense != null">
                #{reasonableMedicalExpense,jdbcType=DECIMAL},
            </if>
            <if test="calculatedCompensationAmount != null">
                #{calculatedCompensationAmount,jdbcType=DECIMAL},
            </if>
            <if test="deductionAmount != null">
                #{deductionAmount,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateSublog">
        update CLMS_CACULATE_SUBLOG
        set id = #{id,jdbcType=VARCHAR},
            caculate_log_id = #{caculateLogId,jdbcType=INTEGER},
            file_id = #{fileId,jdbcType=VARCHAR},
            id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            scen_type = #{scenType,jdbcType=VARCHAR},
            scen_id = #{scenId,jdbcType=INTEGER},
            PAY_PROPORTION = #{payProportion,jdbcType=DECIMAL},
            duty_code = #{dutyCode,jdbcType=VARCHAR},
            duty_name = #{dutyName,jdbcType=VARCHAR},
            duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            invoice_item_type = #{invoiceItemType,jdbcType=VARCHAR},
            invoice_item_code = #{invoiceItemCode,jdbcType=VARCHAR},
            invoice_item_name = #{invoiceItemName,jdbcType=VARCHAR},
            invoice_detail_code = #{invoiceDetailCode,jdbcType=VARCHAR},
            invoice_detail_name = #{invoiceDetailName,jdbcType=VARCHAR},
            self_pay_amount = #{selfPayAmount,jdbcType=DECIMAL},
            partial_self_pay_amount = #{partialSelfPayAmount,jdbcType=DECIMAL},
            unreasonable_amount = #{unreasonableAmount,jdbcType=DECIMAL},
            third_party_payment = #{thirdPartyPayment,jdbcType=DECIMAL},
            reasonable_medical_expense = #{reasonableMedicalExpense,jdbcType=DECIMAL},
            calculated_compensation_amount = #{calculatedCompensationAmount,jdbcType=DECIMAL},
            deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsCaculateSublog">
        update CLMS_CACULATE_SUBLOG
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="caculateLogId != null">
                caculate_log_id = #{caculateLogId,jdbcType=INTEGER},
            </if>
            <if test="fileId != null">
                file_id = #{fileId,jdbcType=VARCHAR},
            </if>
            <if test="idAhcsInvoiceInfo != null">
                id_ahcs_invoice_info = #{idAhcsInvoiceInfo,jdbcType=VARCHAR},
            </if>
            <if test="scenType != null">
                scen_type = #{scenType,jdbcType=VARCHAR},
            </if>
            <if test="scenId != null">
                scen_id = #{scenId,jdbcType=INTEGER},
            </if>
            <if test="payProportion != null">
                PAY_PROPORTION = #{payProportion,jdbcType=DECIMAL},
            </if>
            <if test="dutyCode != null">
                duty_code = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                duty_name = #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemType != null">
                invoice_item_type = #{invoiceItemType,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemCode != null">
                invoice_item_code = #{invoiceItemCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceItemName != null">
                invoice_item_name = #{invoiceItemName,jdbcType=VARCHAR},
            </if>
            <if test="invoiceDetailCode != null">
                invoice_detail_code = #{invoiceDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="invoiceDetailName != null">
                invoice_detail_name = #{invoiceDetailName,jdbcType=VARCHAR},
            </if>
            <if test="selfPayAmount != null">
                self_pay_amount = #{selfPayAmount,jdbcType=DECIMAL},
            </if>
            <if test="partialSelfPayAmount != null">
                partial_self_pay_amount = #{partialSelfPayAmount,jdbcType=DECIMAL},
            </if>
            <if test="unreasonableAmount != null">
                unreasonable_amount = #{unreasonableAmount,jdbcType=DECIMAL},
            </if>
            <if test="thirdPartyPayment != null">
                third_party_payment = #{thirdPartyPayment,jdbcType=DECIMAL},
            </if>
            <if test="reasonableMedicalExpense != null">
                reasonable_medical_expense = #{reasonableMedicalExpense,jdbcType=DECIMAL},
            </if>
            <if test="calculatedCompensationAmount != null">
                calculated_compensation_amount = #{calculatedCompensationAmount,jdbcType=DECIMAL},
            </if>
            <if test="deductionAmount != null">
                deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_CACULATE_SUBLOG
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CACULATE_SUBLOG
    </select>

</mapper>
