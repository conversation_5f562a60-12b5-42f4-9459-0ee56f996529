<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseTaskMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTask">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="flow_in_id" jdbcType="INTEGER" property="flowInId" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="version_no" jdbcType="INTEGER" property="versionNo" />
        <result column="task_node" jdbcType="VARCHAR" property="taskNode" />
        <result column="flow_in_time" jdbcType="TIMESTAMP" property="flowInTime" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="flow_out_time" jdbcType="TIMESTAMP" property="flowOutTime" />
        <result column="node_status" jdbcType="VARCHAR" property="nodeStatus" />
        <result column="task_user_code" jdbcType="VARCHAR" property="taskUserCode" />
        <result column="review_opinion" jdbcType="VARCHAR" property="reviewOpinion" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, flow_in_id, config_id, version_no, task_node, flow_in_time, start_time, flow_out_time, node_status, task_user_code, review_opinion, remark, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CLAUSE_TASK
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTask" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CLAUSE_TASK (id, flow_in_id, config_id, version_no, task_node, flow_in_time, start_time, flow_out_time, node_status, task_user_code, review_opinion, remark, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{flowInId,jdbcType=INTEGER}, #{configId,jdbcType=INTEGER}, #{versionNo,jdbcType=INTEGER}, #{taskNode,jdbcType=VARCHAR}, #{flowInTime,jdbcType=TIMESTAMP}, #{startTime,jdbcType=TIMESTAMP}, #{flowOutTime,jdbcType=TIMESTAMP}, #{nodeStatus,jdbcType=VARCHAR}, #{taskUserCode,jdbcType=VARCHAR}, #{reviewOpinion,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTask" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CLAUSE_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="flowInId != null">
                flow_in_id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="versionNo != null">
                version_no,
            </if>
            <if test="taskNode != null">
                task_node,
            </if>
            <if test="flowInTime != null">
                flow_in_time,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="flowOutTime != null">
                flow_out_time,
            </if>
            <if test="nodeStatus != null">
                node_status,
            </if>
            <if test="taskUserCode != null">
                task_user_code,
            </if>
            <if test="reviewOpinion != null">
                review_opinion,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="flowInId != null">
                #{flowInId,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="versionNo != null">
                #{versionNo,jdbcType=INTEGER},
            </if>
            <if test="taskNode != null">
                #{taskNode,jdbcType=VARCHAR},
            </if>
            <if test="flowInTime != null">
                #{flowInTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="flowOutTime != null">
                #{flowOutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="nodeStatus != null">
                #{nodeStatus,jdbcType=VARCHAR},
            </if>
            <if test="taskUserCode != null">
                #{taskUserCode,jdbcType=VARCHAR},
            </if>
            <if test="reviewOpinion != null">
                #{reviewOpinion,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTask">
        update CLMS_CLAUSE_TASK
        set id = #{id,jdbcType=VARCHAR},
            flow_in_id = #{flowInId,jdbcType=INTEGER},
            config_id = #{configId,jdbcType=INTEGER},
            version_no = #{versionNo,jdbcType=INTEGER},
            task_node = #{taskNode,jdbcType=VARCHAR},
            flow_in_time = #{flowInTime,jdbcType=TIMESTAMP},
            start_time = #{startTime,jdbcType=TIMESTAMP},
            flow_out_time = #{flowOutTime,jdbcType=TIMESTAMP},
            node_status = #{nodeStatus,jdbcType=VARCHAR},
            task_user_code = #{taskUserCode,jdbcType=VARCHAR},
            review_opinion = #{reviewOpinion,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseTask">
        update CLMS_CLAUSE_TASK
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="flowInId != null">
                flow_in_id = #{flowInId,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="versionNo != null">
                version_no = #{versionNo,jdbcType=INTEGER},
            </if>
            <if test="taskNode != null">
                task_node = #{taskNode,jdbcType=VARCHAR},
            </if>
            <if test="flowInTime != null">
                flow_in_time = #{flowInTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="flowOutTime != null">
                flow_out_time = #{flowOutTime,jdbcType=TIMESTAMP},
            </if>
            <if test="nodeStatus != null">
                node_status = #{nodeStatus,jdbcType=VARCHAR},
            </if>
            <if test="taskUserCode != null">
                task_user_code = #{taskUserCode,jdbcType=VARCHAR},
            </if>
            <if test="reviewOpinion != null">
                review_opinion = #{reviewOpinion,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_CLAUSE_TASK
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CLAUSE_TASK
    </select>

</mapper>
