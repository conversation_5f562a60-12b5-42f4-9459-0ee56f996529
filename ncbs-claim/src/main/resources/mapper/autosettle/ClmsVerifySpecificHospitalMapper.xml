<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsVerifySpecificHospitalMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySpecificHospital">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="v_hospital_id" jdbcType="INTEGER" property="vHospitalId" />
        <result column="specific_type" jdbcType="VARCHAR" property="specificType" />
        <result column="hospital_code" jdbcType="VARCHAR" property="hospitalCode" />
        <result column="hospital_name" jdbcType="VARCHAR" property="hospitalName" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, v_hospital_id, specific_type, hospital_code, hospital_name, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_SPECIFIC_HOSPITAL
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySpecificHospital" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_SPECIFIC_HOSPITAL (id, config_id, v_hospital_id, specific_type, hospital_code, hospital_name, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{vHospitalId,jdbcType=INTEGER}, #{specificType,jdbcType=VARCHAR}, #{hospitalCode,jdbcType=VARCHAR}, #{hospitalName,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySpecificHospital" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_VERIFY_SPECIFIC_HOSPITAL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="vHospitalId != null">
                v_hospital_id,
            </if>
            <if test="specificType != null">
                specific_type,
            </if>
            <if test="hospitalCode != null">
                hospital_code,
            </if>
            <if test="hospitalName != null">
                hospital_name,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="vHospitalId != null">
                #{vHospitalId,jdbcType=INTEGER},
            </if>
            <if test="specificType != null">
                #{specificType,jdbcType=VARCHAR},
            </if>
            <if test="hospitalCode != null">
                #{hospitalCode,jdbcType=VARCHAR},
            </if>
            <if test="hospitalName != null">
                #{hospitalName,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySpecificHospital">
        update CLMS_VERIFY_SPECIFIC_HOSPITAL
        set id = #{id,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            v_hospital_id = #{vHospitalId,jdbcType=INTEGER},
            specific_type = #{specificType,jdbcType=VARCHAR},
            hospital_code = #{hospitalCode,jdbcType=VARCHAR},
            hospital_name = #{hospitalName,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsVerifySpecificHospital">
        update CLMS_VERIFY_SPECIFIC_HOSPITAL
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="vHospitalId != null">
                v_hospital_id = #{vHospitalId,jdbcType=INTEGER},
            </if>
            <if test="specificType != null">
                specific_type = #{specificType,jdbcType=VARCHAR},
            </if>
            <if test="hospitalCode != null">
                hospital_code = #{hospitalCode,jdbcType=VARCHAR},
            </if>
            <if test="hospitalName != null">
                hospital_name = #{hospitalName,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_VERIFY_SPECIFIC_HOSPITAL
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_VERIFY_SPECIFIC_HOSPITAL
    </select>

</mapper>
