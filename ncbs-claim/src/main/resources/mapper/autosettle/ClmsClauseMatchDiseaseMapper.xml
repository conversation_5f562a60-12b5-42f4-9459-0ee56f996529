<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsClauseMatchDiseaseMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDisease">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="match_id" jdbcType="INTEGER" property="matchId" />
        <result column="medical_dir1_id" jdbcType="INTEGER" property="medicalDir1Id" />
        <result column="medical_dir1_name" jdbcType="VARCHAR" property="medicalDir1Name" />
        <result column="medical_dir2_id" jdbcType="INTEGER" property="medicalDir2Id" />
        <result column="medical_dir2_name" jdbcType="VARCHAR" property="medicalDir2Name" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, match_id, medical_dir1_id, medical_dir1_name, medical_dir2_id, medical_dir2_name, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CLAUSE_MATCH_DISEASE
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDisease" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CLAUSE_MATCH_DISEASE (id, config_id, match_id, medical_dir1_id, medical_dir1_name, medical_dir2_id, medical_dir2_name, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{matchId,jdbcType=INTEGER}, #{medicalDir1Id,jdbcType=INTEGER}, #{medicalDir1Name,jdbcType=VARCHAR}, #{medicalDir2Id,jdbcType=INTEGER}, #{medicalDir2Name,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDisease" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_CLAUSE_MATCH_DISEASE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="matchId != null">
                match_id,
            </if>
            <if test="medicalDir1Id != null">
                medical_dir1_id,
            </if>
            <if test="medicalDir1Name != null">
                medical_dir1_name,
            </if>
            <if test="medicalDir2Id != null">
                medical_dir2_id,
            </if>
            <if test="medicalDir2Name != null">
                medical_dir2_name,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="matchId != null">
                #{matchId,jdbcType=INTEGER},
            </if>
            <if test="medicalDir1Id != null">
                #{medicalDir1Id,jdbcType=INTEGER},
            </if>
            <if test="medicalDir1Name != null">
                #{medicalDir1Name,jdbcType=VARCHAR},
            </if>
            <if test="medicalDir2Id != null">
                #{medicalDir2Id,jdbcType=INTEGER},
            </if>
            <if test="medicalDir2Name != null">
                #{medicalDir2Name,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDisease">
        update CLMS_CLAUSE_MATCH_DISEASE
        set id = #{id,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            match_id = #{matchId,jdbcType=INTEGER},
            medical_dir1_id = #{medicalDir1Id,jdbcType=INTEGER},
            medical_dir1_name = #{medicalDir1Name,jdbcType=VARCHAR},
            medical_dir2_id = #{medicalDir2Id,jdbcType=INTEGER},
            medical_dir2_name = #{medicalDir2Name,jdbcType=VARCHAR},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsClauseMatchDisease">
        update CLMS_CLAUSE_MATCH_DISEASE
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="matchId != null">
                match_id = #{matchId,jdbcType=INTEGER},
            </if>
            <if test="medicalDir1Id != null">
                medical_dir1_id = #{medicalDir1Id,jdbcType=INTEGER},
            </if>
            <if test="medicalDir1Name != null">
                medical_dir1_name = #{medicalDir1Name,jdbcType=VARCHAR},
            </if>
            <if test="medicalDir2Id != null">
                medical_dir2_id = #{medicalDir2Id,jdbcType=INTEGER},
            </if>
            <if test="medicalDir2Name != null">
                medical_dir2_name = #{medicalDir2Name,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_CLAUSE_MATCH_DISEASE
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_CLAUSE_MATCH_DISEASE
    </select>

</mapper>
