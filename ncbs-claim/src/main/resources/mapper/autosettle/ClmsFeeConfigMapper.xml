<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.autosettle.ClmsFeeConfigMapper">

    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.autosettle.ClmsFeeConfig">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="config_id" jdbcType="INTEGER" property="configId" />
        <result column="config_level" jdbcType="TINYINT" property="configLevel" />
        <result column="product_code" jdbcType="VARCHAR" property="productCode" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
        <result column="plan_name" jdbcType="VARCHAR" property="planName" />
        <result column="risk_group_code" jdbcType="VARCHAR" property="riskGroupCode" />
        <result column="risk_group_name" jdbcType="VARCHAR" property="riskGroupName" />
        <result column="duty_code" jdbcType="VARCHAR" property="dutyCode" />
        <result column="duty_name" jdbcType="VARCHAR" property="dutyName" />
        <result column="duty_detail_code" jdbcType="VARCHAR" property="dutyDetailCode" />
        <result column="duty_detail_name" jdbcType="VARCHAR" property="dutyDetailName" />
        <result column="amount_limit" jdbcType="DECIMAL" property="amountLimit" />
        <result column="amount_limit_differ_id" jdbcType="INTEGER" property="amountLimitDifferId" />
        <result column="amount_month_limit" jdbcType="DECIMAL" property="amountMonthLimit" />
        <result column="amount_month_limit_type" jdbcType="VARCHAR" property="amountMonthLimitType" />
        <result column="amount_month_limit_differ_id" jdbcType="INTEGER" property="amountMonthLimitDifferId" />
        <result column="amount_day_limit" jdbcType="DECIMAL" property="amountDayLimit" />
        <result column="amount_day_limit_differ_id" jdbcType="INTEGER" property="amountDayLimitDifferId" />
        <result column="amount_share_flag" jdbcType="BOOLEAN" property="amountShareFlag" />
        <result column="times_limit" jdbcType="INTEGER" property="timesLimit" />
        <result column="times_define" jdbcType="VARCHAR" property="timesDefine" />
        <result column="times_month_limit" jdbcType="INTEGER" property="timesMonthLimit" />
        <result column="times_month_limit_type" jdbcType="VARCHAR" property="timesMonthLimitType" />
        <result column="times_month_define" jdbcType="VARCHAR" property="timesMonthDefine" />
        <result column="times_day_limit" jdbcType="INTEGER" property="timesDayLimit" />
        <result column="times_day_define" jdbcType="VARCHAR" property="timesDayDefine" />
        <result column="times_share_flag" jdbcType="BOOLEAN" property="timesShareFlag" />
        <result column="year_deductible" jdbcType="DECIMAL" property="yearDeductible" />
        <result column="day_deductible" jdbcType="DECIMAL" property="dayDeductible" />
        <result column="times_deductible" jdbcType="DECIMAL" property="timesDeductible" />
        <result column="times_deductible_define" jdbcType="VARCHAR" property="timesDeductibleDefine" />
        <result column="deductible_share_flag" jdbcType="BOOLEAN" property="deductibleShareFlag" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="sys_ctime" jdbcType="TIMESTAMP" property="sysCtime" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="sys_utime" jdbcType="TIMESTAMP" property="sysUtime" />
    </resultMap>

    <!-- Base Column List -->
    <sql id="Base_Column_List">
        id, config_id, config_level, product_code, product_name, plan_code, plan_name, risk_group_code, risk_group_name, duty_code, duty_name, duty_detail_code, duty_detail_name, amount_limit, amount_limit_differ_id, amount_month_limit, amount_month_limit_type, amount_month_limit_differ_id, amount_day_limit, amount_day_limit_differ_id, amount_share_flag, times_limit, times_define, times_month_limit, times_month_limit_type, times_month_define, times_day_limit, times_day_define, times_share_flag, year_deductible, day_deductible, times_deductible, times_deductible_define, deductible_share_flag, created_by, sys_ctime, updated_by, sys_utime
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_FEE_CONFIG
        where id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsFeeConfig" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_FEE_CONFIG (id, config_id, config_level, product_code, product_name, plan_code, plan_name, risk_group_code, risk_group_name, duty_code, duty_name, duty_detail_code, duty_detail_name, amount_limit, amount_limit_differ_id, amount_month_limit, amount_month_limit_type, amount_month_limit_differ_id, amount_day_limit, amount_day_limit_differ_id, amount_share_flag, times_limit, times_define, times_month_limit, times_month_limit_type, times_month_define, times_day_limit, times_day_define, times_share_flag, year_deductible, day_deductible, times_deductible, times_deductible_define, deductible_share_flag, created_by, sys_ctime, updated_by, sys_utime)
        values (#{id,jdbcType=VARCHAR}, #{configId,jdbcType=INTEGER}, #{configLevel,jdbcType=TINYINT}, #{productCode,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{planCode,jdbcType=VARCHAR}, #{planName,jdbcType=VARCHAR}, #{riskGroupCode,jdbcType=VARCHAR}, #{riskGroupName,jdbcType=VARCHAR}, #{dutyCode,jdbcType=VARCHAR}, #{dutyName,jdbcType=VARCHAR}, #{dutyDetailCode,jdbcType=VARCHAR}, #{dutyDetailName,jdbcType=VARCHAR}, #{amountLimit,jdbcType=DECIMAL}, #{amountLimitDifferId,jdbcType=INTEGER}, #{amountMonthLimit,jdbcType=DECIMAL}, #{amountMonthLimitType,jdbcType=VARCHAR}, #{amountMonthLimitDifferId,jdbcType=INTEGER}, #{amountDayLimit,jdbcType=DECIMAL}, #{amountDayLimitDifferId,jdbcType=INTEGER}, #{amountShareFlag,jdbcType=BOOLEAN}, #{timesLimit,jdbcType=INTEGER}, #{timesDefine,jdbcType=VARCHAR}, #{timesMonthLimit,jdbcType=INTEGER}, #{timesMonthLimitType,jdbcType=VARCHAR}, #{timesMonthDefine,jdbcType=VARCHAR}, #{timesDayLimit,jdbcType=INTEGER}, #{timesDayDefine,jdbcType=VARCHAR}, #{timesShareFlag,jdbcType=BOOLEAN}, #{yearDeductible,jdbcType=DECIMAL}, #{dayDeductible,jdbcType=DECIMAL}, #{timesDeductible,jdbcType=DECIMAL}, #{timesDeductibleDefine,jdbcType=VARCHAR}, #{deductibleShareFlag,jdbcType=BOOLEAN}, #{createdBy,jdbcType=VARCHAR}, #{sysCtime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{sysUtime,jdbcType=TIMESTAMP})
    </insert>

    <!-- 选择性插入记录 -->
    <insert id="insertSelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsFeeConfig" useGeneratedKeys="true" keyProperty="id">
        insert into CLMS_FEE_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="create != null">
                id,
            </if>
            <if test="configId != null">
                config_id,
            </if>
            <if test="configLevel != null">
                config_level,
            </if>
            <if test="productCode != null">
                product_code,
            </if>
            <if test="productName != null">
                product_name,
            </if>
            <if test="planCode != null">
                plan_code,
            </if>
            <if test="planName != null">
                plan_name,
            </if>
            <if test="riskGroupCode != null">
                risk_group_code,
            </if>
            <if test="riskGroupName != null">
                risk_group_name,
            </if>
            <if test="dutyCode != null">
                duty_code,
            </if>
            <if test="dutyName != null">
                duty_name,
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code,
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name,
            </if>
            <if test="amountLimit != null">
                amount_limit,
            </if>
            <if test="amountLimitDifferId != null">
                amount_limit_differ_id,
            </if>
            <if test="amountMonthLimit != null">
                amount_month_limit,
            </if>
            <if test="amountMonthLimitType != null">
                amount_month_limit_type,
            </if>
            <if test="amountMonthLimitDifferId != null">
                amount_month_limit_differ_id,
            </if>
            <if test="amountDayLimit != null">
                amount_day_limit,
            </if>
            <if test="amountDayLimitDifferId != null">
                amount_day_limit_differ_id,
            </if>
            <if test="amountShareFlag != null">
                amount_share_flag,
            </if>
            <if test="timesLimit != null">
                times_limit,
            </if>
            <if test="timesDefine != null">
                times_define,
            </if>
            <if test="timesMonthLimit != null">
                times_month_limit,
            </if>
            <if test="timesMonthLimitType != null">
                times_month_limit_type,
            </if>
            <if test="timesMonthDefine != null">
                times_month_define,
            </if>
            <if test="timesDayLimit != null">
                times_day_limit,
            </if>
            <if test="timesDayDefine != null">
                times_day_define,
            </if>
            <if test="timesShareFlag != null">
                times_share_flag,
            </if>
            <if test="yearDeductible != null">
                year_deductible,
            </if>
            <if test="dayDeductible != null">
                day_deductible,
            </if>
            <if test="timesDeductible != null">
                times_deductible,
            </if>
            <if test="timesDeductibleDefine != null">
                times_deductible_define,
            </if>
            <if test="deductibleShareFlag != null">
                deductible_share_flag,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="sysCtime != null">
                sys_ctime,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="sysUtime != null">
                sys_utime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="create != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=INTEGER},
            </if>
            <if test="configLevel != null">
                #{configLevel,jdbcType=TINYINT},
            </if>
            <if test="productCode != null">
                #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                #{planName,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="amountLimit != null">
                #{amountLimit,jdbcType=DECIMAL},
            </if>
            <if test="amountLimitDifferId != null">
                #{amountLimitDifferId,jdbcType=INTEGER},
            </if>
            <if test="amountMonthLimit != null">
                #{amountMonthLimit,jdbcType=DECIMAL},
            </if>
            <if test="amountMonthLimitType != null">
                #{amountMonthLimitType,jdbcType=VARCHAR},
            </if>
            <if test="amountMonthLimitDifferId != null">
                #{amountMonthLimitDifferId,jdbcType=INTEGER},
            </if>
            <if test="amountDayLimit != null">
                #{amountDayLimit,jdbcType=DECIMAL},
            </if>
            <if test="amountDayLimitDifferId != null">
                #{amountDayLimitDifferId,jdbcType=INTEGER},
            </if>
            <if test="amountShareFlag != null">
                #{amountShareFlag,jdbcType=BOOLEAN},
            </if>
            <if test="timesLimit != null">
                #{timesLimit,jdbcType=INTEGER},
            </if>
            <if test="timesDefine != null">
                #{timesDefine,jdbcType=VARCHAR},
            </if>
            <if test="timesMonthLimit != null">
                #{timesMonthLimit,jdbcType=INTEGER},
            </if>
            <if test="timesMonthLimitType != null">
                #{timesMonthLimitType,jdbcType=VARCHAR},
            </if>
            <if test="timesMonthDefine != null">
                #{timesMonthDefine,jdbcType=VARCHAR},
            </if>
            <if test="timesDayLimit != null">
                #{timesDayLimit,jdbcType=INTEGER},
            </if>
            <if test="timesDayDefine != null">
                #{timesDayDefine,jdbcType=VARCHAR},
            </if>
            <if test="timesShareFlag != null">
                #{timesShareFlag,jdbcType=BOOLEAN},
            </if>
            <if test="yearDeductible != null">
                #{yearDeductible,jdbcType=DECIMAL},
            </if>
            <if test="dayDeductible != null">
                #{dayDeductible,jdbcType=DECIMAL},
            </if>
            <if test="timesDeductible != null">
                #{timesDeductible,jdbcType=DECIMAL},
            </if>
            <if test="timesDeductibleDefine != null">
                #{timesDeductibleDefine,jdbcType=VARCHAR},
            </if>
            <if test="deductibleShareFlag != null">
                #{deductibleShareFlag,jdbcType=BOOLEAN},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 根据主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsFeeConfig">
        update CLMS_FEE_CONFIG
        set id = #{id,jdbcType=VARCHAR},
            config_id = #{configId,jdbcType=INTEGER},
            config_level = #{configLevel,jdbcType=TINYINT},
            product_code = #{productCode,jdbcType=VARCHAR},
            product_name = #{productName,jdbcType=VARCHAR},
            plan_code = #{planCode,jdbcType=VARCHAR},
            plan_name = #{planName,jdbcType=VARCHAR},
            risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            duty_code = #{dutyCode,jdbcType=VARCHAR},
            duty_name = #{dutyName,jdbcType=VARCHAR},
            duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            amount_limit = #{amountLimit,jdbcType=DECIMAL},
            amount_limit_differ_id = #{amountLimitDifferId,jdbcType=INTEGER},
            amount_month_limit = #{amountMonthLimit,jdbcType=DECIMAL},
            amount_month_limit_type = #{amountMonthLimitType,jdbcType=VARCHAR},
            amount_month_limit_differ_id = #{amountMonthLimitDifferId,jdbcType=INTEGER},
            amount_day_limit = #{amountDayLimit,jdbcType=DECIMAL},
            amount_day_limit_differ_id = #{amountDayLimitDifferId,jdbcType=INTEGER},
            amount_share_flag = #{amountShareFlag,jdbcType=BOOLEAN},
            times_limit = #{timesLimit,jdbcType=INTEGER},
            times_define = #{timesDefine,jdbcType=VARCHAR},
            times_month_limit = #{timesMonthLimit,jdbcType=INTEGER},
            times_month_limit_type = #{timesMonthLimitType,jdbcType=VARCHAR},
            times_month_define = #{timesMonthDefine,jdbcType=VARCHAR},
            times_day_limit = #{timesDayLimit,jdbcType=INTEGER},
            times_day_define = #{timesDayDefine,jdbcType=VARCHAR},
            times_share_flag = #{timesShareFlag,jdbcType=BOOLEAN},
            year_deductible = #{yearDeductible,jdbcType=DECIMAL},
            day_deductible = #{dayDeductible,jdbcType=DECIMAL},
            times_deductible = #{timesDeductible,jdbcType=DECIMAL},
            times_deductible_define = #{timesDeductibleDefine,jdbcType=VARCHAR},
            deductible_share_flag = #{deductibleShareFlag,jdbcType=BOOLEAN},
            created_by = #{createdBy,jdbcType=VARCHAR},
            sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            updated_by = #{updatedBy,jdbcType=VARCHAR},
            sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键选择性更新 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.autosettle.ClmsFeeConfig">
        update CLMS_FEE_CONFIG
        <set>
            <if test="create != null">
                id = #{id,jdbcType=VARCHAR},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=INTEGER},
            </if>
            <if test="configLevel != null">
                config_level = #{configLevel,jdbcType=TINYINT},
            </if>
            <if test="productCode != null">
                product_code = #{productCode,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                product_name = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="planCode != null">
                plan_code = #{planCode,jdbcType=VARCHAR},
            </if>
            <if test="planName != null">
                plan_name = #{planName,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupCode != null">
                risk_group_code = #{riskGroupCode,jdbcType=VARCHAR},
            </if>
            <if test="riskGroupName != null">
                risk_group_name = #{riskGroupName,jdbcType=VARCHAR},
            </if>
            <if test="dutyCode != null">
                duty_code = #{dutyCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyName != null">
                duty_name = #{dutyName,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailCode != null">
                duty_detail_code = #{dutyDetailCode,jdbcType=VARCHAR},
            </if>
            <if test="dutyDetailName != null">
                duty_detail_name = #{dutyDetailName,jdbcType=VARCHAR},
            </if>
            <if test="amountLimit != null">
                amount_limit = #{amountLimit,jdbcType=DECIMAL},
            </if>
            <if test="amountLimitDifferId != null">
                amount_limit_differ_id = #{amountLimitDifferId,jdbcType=INTEGER},
            </if>
            <if test="amountMonthLimit != null">
                amount_month_limit = #{amountMonthLimit,jdbcType=DECIMAL},
            </if>
            <if test="amountMonthLimitType != null">
                amount_month_limit_type = #{amountMonthLimitType,jdbcType=VARCHAR},
            </if>
            <if test="amountMonthLimitDifferId != null">
                amount_month_limit_differ_id = #{amountMonthLimitDifferId,jdbcType=INTEGER},
            </if>
            <if test="amountDayLimit != null">
                amount_day_limit = #{amountDayLimit,jdbcType=DECIMAL},
            </if>
            <if test="amountDayLimitDifferId != null">
                amount_day_limit_differ_id = #{amountDayLimitDifferId,jdbcType=INTEGER},
            </if>
            <if test="amountShareFlag != null">
                amount_share_flag = #{amountShareFlag,jdbcType=BOOLEAN},
            </if>
            <if test="timesLimit != null">
                times_limit = #{timesLimit,jdbcType=INTEGER},
            </if>
            <if test="timesDefine != null">
                times_define = #{timesDefine,jdbcType=VARCHAR},
            </if>
            <if test="timesMonthLimit != null">
                times_month_limit = #{timesMonthLimit,jdbcType=INTEGER},
            </if>
            <if test="timesMonthLimitType != null">
                times_month_limit_type = #{timesMonthLimitType,jdbcType=VARCHAR},
            </if>
            <if test="timesMonthDefine != null">
                times_month_define = #{timesMonthDefine,jdbcType=VARCHAR},
            </if>
            <if test="timesDayLimit != null">
                times_day_limit = #{timesDayLimit,jdbcType=INTEGER},
            </if>
            <if test="timesDayDefine != null">
                times_day_define = #{timesDayDefine,jdbcType=VARCHAR},
            </if>
            <if test="timesShareFlag != null">
                times_share_flag = #{timesShareFlag,jdbcType=BOOLEAN},
            </if>
            <if test="yearDeductible != null">
                year_deductible = #{yearDeductible,jdbcType=DECIMAL},
            </if>
            <if test="dayDeductible != null">
                day_deductible = #{dayDeductible,jdbcType=DECIMAL},
            </if>
            <if test="timesDeductible != null">
                times_deductible = #{timesDeductible,jdbcType=DECIMAL},
            </if>
            <if test="timesDeductibleDefine != null">
                times_deductible_define = #{timesDeductibleDefine,jdbcType=VARCHAR},
            </if>
            <if test="deductibleShareFlag != null">
                deductible_share_flag = #{deductibleShareFlag,jdbcType=BOOLEAN},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="sysCtime != null">
                sys_ctime = #{sysCtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="sysUtime != null">
                sys_utime = #{sysUtime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 根据主键删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from CLMS_FEE_CONFIG
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from CLMS_FEE_CONFIG
    </select>

</mapper>
